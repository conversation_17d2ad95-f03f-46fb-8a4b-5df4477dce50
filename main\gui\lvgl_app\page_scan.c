#include "page_scan.h"
#include "page_menu.h"
#include "page_main.h"
#include "page_scan_chart.h"
#include "page_scan_table.h"
#include "page_scan_calib.h"
#include "lvgl_stl.h"
#include "rx5808.h"
#include "lv_port_disp.h"
#include "beep.h"

LV_FONT_DECLARE(lv_font_chinese_12);

#define page_scan_anim_enter lv_anim_path_bounce
#define page_scan_anim_leave lv_anim_path_bounce

#define LABEL_FOCUSE_COLOR lv_color_make(255, 100, 0)
#define LABEL_DEFAULT_COLOR lv_color_make(255, 255, 255)

static lv_obj_t *menu_scan_contain = NULL;
static lv_obj_t *chart_label;

static lv_group_t *scan_group;

static void page_scan_callback(lv_event_t *event);

lv_timer_t *page_scan_time;
lv_indev_state_t key_state_scan = LV_INDEV_STATE_RELEASED;

static void page_scan_time_callback(lv_timer_t *tmr)
{
    if (tmr == page_scan_time)
    {
        lv_timer_reset(page_scan_time);
        lv_timer_del(page_scan_time);
        page_scan_exit();
        key_delay_time_menu(800);
        lv_fun_param_delayed(page_menu_create, 500, item_scan);
        video_composite_switch(false);
    }
    key_state_scan = LV_INDEV_STATE_RELEASED;
}

void key_delay_time_scan(uint32_t time_out)
{
    page_scan_time = lv_timer_create(page_scan_time_callback, time_out, NULL);
    lv_indev_t *indev = lv_indev_get_act();
    if (indev)
    {
        key_state_scan = lv_indev_get_key(indev);
        if (key_state_scan == LV_INDEV_STATE_PRESSED)
        {
            lv_timer_set_repeat_count(page_scan_time, 1);
        }
    }
}

static void page_scan_callback(lv_event_t *event)
{
    lv_event_code_t code = lv_event_get_code(event);
    lv_obj_t *obj = lv_event_get_target(event);
    if (code == LV_EVENT_KEY)
    {
        beep_on_off(beep_get_status());
        lv_fun_param_delayed(beep_on_off, 100, 0);
        lv_key_t key_status = lv_indev_get_key(lv_indev_get_act());
        if (key_status == LV_KEY_ENTER)
        {
            if (obj == chart_label)
            {

                page_scan_exit();
                lv_fun_delayed(page_scan_chart_create, 500);
            }
        }
        else if (key_status == LV_KEY_LEFT)
        {

            page_scan_exit();
            lv_fun_param_delayed(page_menu_create, 500, item_scan);
        }
        else if (key_status == LV_KEY_UP)
        {
            // key_delay_time_scan(return_delay_time);
            lv_group_focus_prev(scan_group);
        }
        else if (key_status == LV_KEY_DOWN)
        {
            // key_delay_time_scan(return_delay_time);
            lv_group_focus_next(scan_group);
        }
    }
}

void page_scan_exit()
{

    lv_amin_start(chart_label, 0, 160, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_x, page_scan_anim_leave);

    lv_group_del(scan_group);
    lv_obj_del_delayed(menu_scan_contain, 500);
}

void page_scan_create()
{

    lv_color_t label_bg_color = lock_flag ? LABEL_DEFAULT_COLOR : lv_color_black();             // LABEL_DEFAULT_COLOR 白色
    lv_color_t label_focuse_bg_color = lock_flag ? LABEL_FOCUSE_COLOR : lv_color_make(0, 0, 0); // LABEL_FOCUSE_COLOR 橘色
    lv_color_t label_text_color = lock_flag ? lv_color_black() : lv_color_white();
    menu_scan_contain = lv_obj_create(lv_scr_act());
    lv_obj_remove_style_all(menu_scan_contain);
    lv_obj_set_style_bg_color(menu_scan_contain, lv_color_make(0, 0, 0), LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(menu_scan_contain, (lv_opa_t)LV_OPA_COVER, LV_STATE_DEFAULT);
    lv_obj_set_size(menu_scan_contain, 160, 80);
    lv_obj_set_pos(menu_scan_contain, 0, 0);

    chart_label = lv_label_create(menu_scan_contain);
    lv_obj_set_style_bg_opa(chart_label, (lv_opa_t)LV_OPA_COVER, LV_STATE_DEFAULT);
    lv_obj_set_style_radius(chart_label, 4, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(chart_label, label_bg_color, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(chart_label, label_focuse_bg_color, LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(chart_label, label_text_color, LV_STATE_FOCUSED);
    lv_obj_set_style_border_width(chart_label, 1, LV_STATE_FOCUSED);
    // lv_obj_set_style_text_font(chart_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(chart_label, label_text_color, LV_STATE_DEFAULT);
    // lv_label_set_text_fmt(chart_label, "Show with Chart");
    lv_obj_align(chart_label, LV_ALIGN_TOP_MID, 0, 30);
    lv_label_set_long_mode(chart_label, LV_LABEL_LONG_WRAP);

    if (RX5808_Get_Language() == 0)
    {
        lv_obj_set_style_text_font(chart_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);
        lv_label_set_text_fmt(chart_label, "Show with Chart");
    }
    else
    {
        lv_obj_set_style_text_font(chart_label, &lv_font_chinese_12, LV_STATE_DEFAULT);
        lv_label_set_text_fmt(chart_label, "  频谱扫描  ");
    }

    scan_group = lv_group_create();
    lv_indev_set_group(indev_keypad, scan_group);

    lv_obj_add_event_cb(chart_label, page_scan_callback, LV_EVENT_KEY, NULL);

    lv_group_add_obj(scan_group, chart_label);
    lv_group_set_editing(scan_group, true);

    lv_amin_start(chart_label, -160, 0, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_x, page_scan_anim_enter);
}