#ifndef __LVGL_INIT_H
#define __LVGL_INIT_H	


#ifdef __cplusplus
extern "C" {
#endif

    /*********************
    *      INCLUDES
    *********************/
#include "../lvgl/lvgl.h"
#include "lv_port_disp.h"
#include "lv_port_indev.h"
#include "lv_port_fs.h"
    /*********************
    *      DEFINES
    *********************/

    /**********************
    *      TYPEDEFS
    **********************/
    /**********************
    * GLOBAL PROTOTYPES
    **********************/
void lvgl_init(void);
    /**********************
    *      MACROS
    **********************/


#ifdef __cplusplus
} /*extern "C"*/
#endif


#endif

	 
	 



