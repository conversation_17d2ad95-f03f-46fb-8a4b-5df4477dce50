# ninja log v5
43	340	7701293157450037	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	548acfe31d6ae1a0
77	377	7701293158001614	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/emac_periph.c.obj	fd120f5dcad47c1e
51	384	7701293157685795	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj	ba620b2c48efbb67
39	393	7701293157605812	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	a573924187224a06
74	408	7701293157955707	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj	1a873a998c0f44f7
78	421	7701293158277381	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj	688336cccb981632
64	476	7701293158417273	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj	b112ec1e94d9800f
47	479	7701293158417273	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	835d93150df9d18a
40	481	7701293158608165	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	baf1e52be5c9083c
73	485	7701293158769221	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj	c081e3f77505892b
69	526	7701293159049949	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj	a99be3b6c5312170
108	620	7701293159847996	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj	47262c7d21847cc2
37	635	7701293157104191	project_elf_src_esp32.c	478aeb892551800a
37	635	7701293157104191	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/project_elf_src_esp32.c	478aeb892551800a
342	707	7701293160894339	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj	50296520f9455ec2
106	730	7701293160203281	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj	76dc83d2daed971d
393	772	7701293161547376	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj	9b62df7dc9eeeb34
378	775	7701293161853192	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj	6a8b51e88ef6fb39
384	845	7701293162251669	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj	d1f1862235bc7d42
408	889	7701293162677563	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj	9a01c075cc329e1d
486	894	7701293162492505	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj	fd2e33235d0e6cae
476	905	7701293162885054	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj	fc80bf2b363fb060
526	924	7701293163492720	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/twai_periph.c.obj	c331b6ea365385e7
482	960	7701293163114709	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj	b1aa95cd2938f0b3
422	1068	7701293162612288	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj	37263330d5514662
620	1072	7701293163770340	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj	aff3f72e4251fbcc
635	1194	7701293163860658	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj	aadcf4fb3869e6a9
479	1263	7701293163700350	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mpi_periph.c.obj	fb082a45c7904e1b
730	1309	7701293166698307	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj	abc863666994cd92
707	1358	7701293167185677	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj	31b525e36aa57238
775	1361	7701293167592779	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	7c23370720982ccc
890	1482	7701293168302235	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	696f1150d22bd2dc
894	1668	7701293169323990	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj	5b8cdc9fd1719893
846	1704	7701293169563066	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4ea5ac9e580971ef
905	1727	7701293170891102	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	772504c3aa8cefbb
960	1738	7701293171026960	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj	a0ce9e5c8cfe9621
1072	1772	7701293171217406	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	d929e42311fd5223
1068	1910	7701293172541271	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	b89ece9d8f70dd2f
924	1976	7701293173111594	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	9c93e7ec5f8a9a80
1359	2122	7701293174535676	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	ad158def35ec09b8
1194	2183	7701293174776622	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	6f73fed01a3b0b53
1362	2252	7701293175141690	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	579ea0970ed8bf20
1310	2264	7701293175327368	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	336f81c9a1f3e580
1263	2423	7701293177566081	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	d3b91b9dd6310e53
1483	2487	7701293178168782	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	5929125a0d025865
772	2649	7701293178334751	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	5f95d6094d237e9d
1704	2652	7701293179089074	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	4d3b1bc04ba2b671
1728	2666	7701293179044212	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj	fdf7e543511d11bc
1668	2859	7701293179676224	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	658a035789b04080
1772	3115	7701293183405018	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	9ec010d3b3f5d022
2122	3420	7701293186298458	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	db0fd3884e22e068
1738	3679	7701293188470732	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	db078ae6f6a8447d
2252	3710	7701293189140346	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	fef8f42a059b29d0
2264	3811	7701293189698346	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	ad4d353bc8a55975
1910	3836	7701293189301408	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj	927aa1999e9c2484
2652	3898	7701293191492322	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj	ed912933a17a9b72
2423	3928	7701293192181047	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	9fd548d42fff366c
2487	4158	7701293193773708	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	8e2208b16f3285cc
2649	4358	7701293194573116	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj	2d1da15919996b6e
2859	4560	7701293198150071	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	fb122a47b7f4d138
1976	4602	7701293195678565	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	cfc8afca390e6f7f
3116	4628	7701293199437724	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj	ab5920ab550d21e
2183	4730	7701293200227754	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	b9f2dbe2e5825a18
2667	5025	7701293203376179	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj	a8a8d5ee4fef613f
3420	5127	7701293204420387	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj	ee745886a2413561
3679	5157	7701293205164889	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj	500310eee480a363
3711	5253	7701293205998709	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	31f53847434f0391
3929	5260	7701293206164575	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	5e1b23bd5db0391d
3836	5281	7701293206420921	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	f01c73f568a14c8b
3899	5305	7701293206585829	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj	e486d32b97639ce2
3811	5323	7701293206801583	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	dd58910f3e103980
4602	5347	7701293207002145	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/cpu_region_protect.c.obj	ff629aa2612d4693
4358	5360	7701293207087224	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/esp_cpu_intr.c.obj	b75ad47180ad19a4
4158	5367	7701293207624232	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	2d8eb88eeb5b4c49
4560	5384	7701293207659234	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	354b63c9d1cbedad
5305	6020	7701293213866030	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	8cd2921223d12732
4730	6023	7701293213580367	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj	f73715c0a0c637e8
5026	6037	7701293214115054	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj	6844c4f75aca52d
5281	6049	7701293214140263	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	27424d360314a1a3
5368	6050	7701293214008735	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	d80f449530383df4
5254	6065	7701293214670561	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj	63403ffd57f6ad69
4628	6113	7701293214921458	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj	dbfe1d67a28ffb59
5127	6117	7701293215128769	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj	1e047ef86f404bb8
5324	6126	7701293215118855	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	5985cd39fc15c45
5360	6145	7701293215339298	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	c40101daf01e0556
5385	6189	7701293215571081	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	98f1ea6c126684aa
5260	6232	7701293216279979	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	79187f7734b4b247
5348	6233	7701293216279979	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	50820c7e61d296a4
5157	6262	7701293216431425	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj	701c51ef4bff19de
6024	6492	7701293219234531	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	bbe2b677da35ea59
6037	6514	7701293219409569	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	b6de65ec0d0cc628
6020	6515	7701293219424742	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	6de9e69b68d01756
6515	7228	7701293226533807	esp-idf/log/liblog.a	e92ee5a0baf2e56d
7228	7713	7701293231356784	esp-idf/esp_rom/libesp_rom.a	d14ccc0fb3df3f71
7713	8284	7701293236943640	esp-idf/esp_common/libesp_common.a	2208fdad3323df27
8284	8981	7701293243797993	esp-idf/esp_hw_support/libesp_hw_support.a	bd552cb83fa1a4a3
8982	9567	7701293249601745	esp-idf/esp_system/libesp_system.a	57ecb03c434b9837
9567	10298	7701293256713470	esp-idf/efuse/libefuse.a	fe688032ace3632e
10298	11003	7701293263878368	esp-idf/bootloader_support/libbootloader_support.a	fe56045ca6d5a0a5
11003	11742	7701293271150378	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	eaacc95371397a0
11743	12267	7701293276699013	esp-idf/spi_flash/libspi_flash.a	c6817603cc70ebdd
12267	12772	7701293282015600	esp-idf/hal/libhal.a	5bef9d162b112448
12772	13379	7701293287200890	esp-idf/micro-ecc/libmicro-ecc.a	d7dcbb04b36aaee4
13379	14268	7701293296865121	esp-idf/soc/libsoc.a	178acbc71bf27d05
14269	14759	7701293301776559	esp-idf/xtensa/libxtensa.a	7b1cbbb8c672b050
14759	15347	7701293307612532	esp-idf/main/libmain.a	1ded75cab4038372
15347	15839	7701293311766542	bootloader.elf	b506f9c1f0f37569
15839	17233	7701293321825430	.bin_timestamp	751a4cc7aa488998
15839	17233	7701293321825430	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/.bin_timestamp	751a4cc7aa488998
17233	17656	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
17233	17656	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
42	174	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
42	174	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
48	202	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
48	202	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
26	104	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
26	104	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
25	101	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
25	101	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
40	205	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
40	205	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
37	154	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
37	154	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
32	119	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
32	119	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
34	162	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
34	162	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
42	196	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
42	196	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
26	108	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
26	108	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
27	121	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
27	121	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
24	102	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
24	102	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
24	97	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
24	97	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
26	104	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
26	104	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
28	102	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
28	102	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
23	103	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
23	103	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
25	101	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
25	101	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
24	101	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
24	101	0	C:/Users/<USER>/Desktop/RX5808_lock5_80CH2/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	70ce4cdae40c1913
