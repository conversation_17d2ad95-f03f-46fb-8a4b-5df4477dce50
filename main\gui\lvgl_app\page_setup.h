#ifndef __page_menu_setup_H
#define __page_menu_setup_H

// #include "lv_obj.h"

// extern lv_obj_t* boot_animation_switch;

#ifdef __cplusplus
extern "C" {
#endif

    /*********************
    *      INCLUDES
    *********************/
#include "../lvgl/lvgl.h"
    /*********************
    *      DEFINES
    *********************/

    /**********************
    *      TYPEDEFS
    **********************/

    /**********************
    * GLOBAL PROTOTYPES
    **********************/
    void page_setup_create(void);
    void page_setup_exit();
    void key_delay_time_setup(uint32_t time_out);
    void Is_Exit_Save(void);
    /**********************
    *      MACROS
    **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif
 



#endif
