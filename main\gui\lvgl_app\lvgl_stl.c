#include "lvgl_stl.h"

void lv_amin_start(void *obj,
                   int32_t start_value,
                   int32_t end_value,
                   uint32_t repeat_count,
                   uint32_t duration,
                   uint32_t delay,
                   lv_anim_exec_xcb_t exec_cb,
                   lv_anim_path_cb_t path_cb)
{
    lv_anim_t anim;
    lv_anim_init(&anim);
    lv_anim_set_var(&anim, obj);
    lv_anim_set_values(&anim, start_value, end_value);
    lv_anim_set_repeat_count(&anim, repeat_count);
    lv_anim_set_exec_cb(&anim, exec_cb);
    lv_anim_set_time(&anim, duration);
    lv_anim_set_delay(&anim, delay);
    lv_anim_set_path_cb(&anim, path_cb);
    lv_anim_start(&anim);
}

void lv_obj_create_delayed(lv_obj_t *obj, uint32_t SysTick_Delay_ms)
{
    lv_anim_t a;
    lv_anim_init(&a);
    lv_anim_set_var(&a, obj);
    lv_anim_set_exec_cb(&a, NULL);
    lv_anim_set_time(&a, 1);
    lv_anim_set_delay(&a, SysTick_Delay_ms);
    lv_anim_set_ready_cb(&a, lv_obj_create_anim_ready_cb);
    lv_anim_start(&a);
}

// 这个函数的作用是创建一个延迟执行的动画，用于执行一个特定的函数 fun，
// 并在动画完成后执行一些额外的操作。
void lv_fun_param_delayed(void (*fun)(uint8_t), uint32_t SysTick_Delay_ms, uint8_t user_data)
{
    lv_anim_t a; // 设置一个动画对象
    lv_anim_init(&a);
    lv_anim_set_var(&a, fun);
    lv_anim_set_exec_cb(&a, NULL);
    lv_anim_set_time(&a, 1);                 // 设置动画对象执行 时间 毫秒
    lv_anim_set_delay(&a, SysTick_Delay_ms); // 开始动画之前延时 时间 毫秒
    lv_anim_set_ready_cb(&a, lv_fun_param_ready_cb);
    lv_anim_set_user_data(&a, (void *)user_data);
    lv_anim_start(&a);
}

void lv_fun_delayed(void (*fun)(), uint32_t SysTick_Delay_ms)
{
    lv_anim_t a;
    lv_anim_init(&a);
    lv_anim_set_var(&a, fun);
    lv_anim_set_exec_cb(&a, NULL);
    lv_anim_set_time(&a, 1);
    lv_anim_set_delay(&a, SysTick_Delay_ms);
    lv_anim_set_ready_cb(&a, lv_fun_ready_cb);
    lv_anim_start(&a);
}

typedef void (*FUN)(uint8_t);

void lv_style_init_simple(lv_style_t *style)
{
    lv_style_init(style);
    lv_style_set_bg_color(style, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_bg_opa(style, LV_OPA_COVER);

    lv_style_set_text_color(style, lv_color_white());
    lv_style_set_text_opa(style, LV_OPA_COVER);

    lv_style_set_border_width(style, 2);
    lv_style_set_border_color(style, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_border_opa(style, LV_OPA_COVER);

}

void lv_fun_param_ready_cb(lv_anim_t *a)
{
    ((void (*)())a->var)((uint8_t)a->user_data);
}

void lv_fun_ready_cb(lv_anim_t *a)
{
    ((void (*)())a->var)();
}

void lv_obj_create_anim_ready_cb(lv_anim_t *a)
{
    lv_obj_create(a->var);
}

void lv_obj_opa_cb(lv_obj_t *obj, uint8_t value)
{
    lv_obj_set_style_text_opa(obj, value, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(obj, value, LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(obj, value, LV_STATE_DEFAULT);
}


// 
void lv_amin_slide_in(lv_obj_t *obj, int32_t start_y, int32_t end_y, uint32_t delay)
{
    lv_amin_start(obj, start_y, end_y, 1, 350, delay, 
                 (lv_anim_exec_xcb_t)lv_obj_set_y, lv_anim_path_ease_out);
}

//  
void lv_amin_fade_in(lv_obj_t *obj, uint32_t delay)
{
    lv_amin_start(obj, 0, 255, 1, 300, delay, 
                 (lv_anim_exec_xcb_t)lv_obj_opa_cb, lv_anim_path_ease_out);
}
