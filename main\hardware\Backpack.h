/**
 * @file Backpack.h
 * @brief 背包通信模块头文件
 *
 * 定义了与ELRS背包通信的接口和命令
 */

#ifndef BACKPACK_H
#define BACKPACK_H

#include <stdint.h>

// 初始化背包通信
void Backpack_init(void);

// 发送数据到背包
void Backpack_send_data(uint8_t *data, int len);

// 获取背包版本信息
void backpack_get_version(void);

// 获取背包状态信息
void backpack_get_status(void);

// 设置频段和通道
void backpack_set_band_channel(uint8_t band, uint8_t channel);

// 获取背包版本字符串
const char *backpack_get_version_string(void);

// 获取背包状态标志
uint8_t backpack_get_status_flags(void);

// 获取背包UID
const uint8_t *backpack_get_uid(void);

// 检查背包版本是否已接收
bool backpack_is_version_received(void);

// 检查背包状态是否已接收
bool backpack_is_status_received(void);

#endif // BACKPACK_H
