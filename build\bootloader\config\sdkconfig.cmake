#
                # Automatically generated file. DO NOT EDIT.
                # Espressif IoT Development Framework (ESP-IDF) Configuration cmake include file
                #
set(CONFIG_SOC_BROWNOUT_RESET_SUPPORTED "Not determined")
set(CONFIG_SOC_TWAI_BRP_DIV_SUPPORTED "Not determined")
set(CONFIG_SOC_DPORT_WORKAROUND "Not determined")
set(CONFIG_SOC_CAPS_ECO_VER_MAX "301")
set(CONFIG_SOC_ADC_SUPPORTED "y")
set(CONFIG_SOC_DAC_SUPPORTED "y")
set(CONFIG_SOC_UART_SUPPORTED "y")
set(CONFIG_SOC_MCPWM_SUPPORTED "y")
set(CONFIG_SOC_GPTIMER_SUPPORTED "y")
set(CONFIG_SOC_SDMMC_HOST_SUPPORTED "y")
set(CONFIG_SOC_BT_SUPPORTED "y")
set(CONFIG_SOC_PCNT_SUPPORTED "y")
set(CONFIG_SOC_PHY_SUPPORTED "y")
set(CONFIG_SOC_WIFI_SUPPORTED "y")
set(CONFIG_SOC_SDIO_SLAVE_SUPPORTED "y")
set(CONFIG_SOC_TWAI_SUPPORTED "y")
set(CONFIG_SOC_EFUSE_SUPPORTED "y")
set(CONFIG_SOC_EMAC_SUPPORTED "y")
set(CONFIG_SOC_ULP_SUPPORTED "y")
set(CONFIG_SOC_CCOMP_TIMER_SUPPORTED "y")
set(CONFIG_SOC_RTC_FAST_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_MEM_SUPPORTED "y")
set(CONFIG_SOC_I2S_SUPPORTED "y")
set(CONFIG_SOC_RMT_SUPPORTED "y")
set(CONFIG_SOC_SDM_SUPPORTED "y")
set(CONFIG_SOC_GPSPI_SUPPORTED "y")
set(CONFIG_SOC_LEDC_SUPPORTED "y")
set(CONFIG_SOC_I2C_SUPPORTED "y")
set(CONFIG_SOC_SUPPORT_COEXISTENCE "y")
set(CONFIG_SOC_AES_SUPPORTED "y")
set(CONFIG_SOC_MPI_SUPPORTED "y")
set(CONFIG_SOC_SHA_SUPPORTED "y")
set(CONFIG_SOC_FLASH_ENC_SUPPORTED "y")
set(CONFIG_SOC_SECURE_BOOT_SUPPORTED "y")
set(CONFIG_SOC_TOUCH_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_BOD_SUPPORTED "y")
set(CONFIG_SOC_ULP_FSM_SUPPORTED "y")
set(CONFIG_SOC_CLK_TREE_SUPPORTED "y")
set(CONFIG_SOC_MPU_SUPPORTED "y")
set(CONFIG_SOC_WDT_SUPPORTED "y")
set(CONFIG_SOC_SPI_FLASH_SUPPORTED "y")
set(CONFIG_SOC_RNG_SUPPORTED "y")
set(CONFIG_SOC_LIGHT_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_DEEP_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT "y")
set(CONFIG_SOC_PM_SUPPORTED "y")
set(CONFIG_SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL "5")
set(CONFIG_SOC_XTAL_SUPPORT_26M "y")
set(CONFIG_SOC_XTAL_SUPPORT_40M "y")
set(CONFIG_SOC_XTAL_SUPPORT_AUTO_DETECT "y")
set(CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DMA_SUPPORTED "y")
set(CONFIG_SOC_ADC_PERIPH_NUM "2")
set(CONFIG_SOC_ADC_MAX_CHANNEL_NUM "10")
set(CONFIG_SOC_ADC_ATTEN_NUM "4")
set(CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM "2")
set(CONFIG_SOC_ADC_PATT_LEN_MAX "16")
set(CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH "9")
set(CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_RESULT_BYTES "2")
set(CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV "4")
set(CONFIG_SOC_ADC_DIGI_MONITOR_NUM "0")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH "2")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW "20")
set(CONFIG_SOC_ADC_RTC_MIN_BITWIDTH "9")
set(CONFIG_SOC_ADC_RTC_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_SHARED_POWER "y")
set(CONFIG_SOC_SHARED_IDCACHE_SUPPORTED "y")
set(CONFIG_SOC_IDCACHE_PER_CORE "y")
set(CONFIG_SOC_CPU_CORES_NUM "2")
set(CONFIG_SOC_CPU_INTR_NUM "32")
set(CONFIG_SOC_CPU_HAS_FPU "y")
set(CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES "y")
set(CONFIG_SOC_CPU_BREAKPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE "64")
set(CONFIG_SOC_DAC_CHAN_NUM "2")
set(CONFIG_SOC_DAC_RESOLUTION "8")
set(CONFIG_SOC_DAC_DMA_16BIT_ALIGN "y")
set(CONFIG_SOC_GPIO_PORT "1")
set(CONFIG_SOC_GPIO_PIN_COUNT "40")
set(CONFIG_SOC_GPIO_VALID_GPIO_MASK "0xffffffffff")
set(CONFIG_SOC_GPIO_IN_RANGE_MAX "39")
set(CONFIG_SOC_GPIO_OUT_RANGE_MAX "33")
set(CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK "0xef0fea")
set(CONFIG_SOC_GPIO_CLOCKOUT_BY_IO_MUX "y")
set(CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM "3")
set(CONFIG_SOC_I2C_NUM "2")
set(CONFIG_SOC_HP_I2C_NUM "2")
set(CONFIG_SOC_I2C_FIFO_LEN "32")
set(CONFIG_SOC_I2C_CMD_REG_NUM "16")
set(CONFIG_SOC_I2C_SUPPORT_SLAVE "y")
set(CONFIG_SOC_I2C_SUPPORT_APB "y")
set(CONFIG_SOC_I2C_STOP_INDEPENDENT "y")
set(CONFIG_SOC_I2S_NUM "2")
set(CONFIG_SOC_I2S_HW_VERSION_1 "y")
set(CONFIG_SOC_I2S_SUPPORTS_APLL "y")
set(CONFIG_SOC_I2S_SUPPORTS_PLL_F160M "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_TX "y")
set(CONFIG_SOC_I2S_PDM_MAX_TX_LINES "1")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_RX "y")
set(CONFIG_SOC_I2S_PDM_MAX_RX_LINES "1")
set(CONFIG_SOC_I2S_SUPPORTS_ADC_DAC "y")
set(CONFIG_SOC_I2S_SUPPORTS_ADC "y")
set(CONFIG_SOC_I2S_SUPPORTS_DAC "y")
set(CONFIG_SOC_I2S_SUPPORTS_LCD_CAMERA "y")
set(CONFIG_SOC_I2S_TRANS_SIZE_ALIGN_WORD "y")
set(CONFIG_SOC_I2S_LCD_I80_VARIANT "y")
set(CONFIG_SOC_LCD_I80_SUPPORTED "y")
set(CONFIG_SOC_LCD_I80_BUSES "2")
set(CONFIG_SOC_LCD_I80_BUS_WIDTH "24")
set(CONFIG_SOC_LEDC_HAS_TIMER_SPECIFIC_MUX "y")
set(CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK "y")
set(CONFIG_SOC_LEDC_SUPPORT_REF_TICK "y")
set(CONFIG_SOC_LEDC_SUPPORT_HS_MODE "y")
set(CONFIG_SOC_LEDC_CHANNEL_NUM "8")
set(CONFIG_SOC_LEDC_TIMER_BIT_WIDTH "20")
set(CONFIG_SOC_MCPWM_GROUPS "2")
set(CONFIG_SOC_MCPWM_TIMERS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP "y")
set(CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER "3")
set(CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP "3")
set(CONFIG_SOC_MMU_PERIPH_NUM "2")
set(CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM "3")
set(CONFIG_SOC_MPU_MIN_REGION_SIZE "0x20000000")
set(CONFIG_SOC_MPU_REGIONS_MAX_NUM "8")
set(CONFIG_SOC_PCNT_GROUPS "1")
set(CONFIG_SOC_PCNT_UNITS_PER_GROUP "8")
set(CONFIG_SOC_PCNT_CHANNELS_PER_UNIT "2")
set(CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT "2")
set(CONFIG_SOC_RMT_GROUPS "1")
set(CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP "8")
set(CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP "8")
set(CONFIG_SOC_RMT_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL "64")
set(CONFIG_SOC_RMT_SUPPORT_REF_TICK "y")
set(CONFIG_SOC_RMT_SUPPORT_APB "y")
set(CONFIG_SOC_RMT_CHANNEL_CLK_INDEPENDENT "y")
set(CONFIG_SOC_RTCIO_PIN_COUNT "18")
set(CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_HOLD_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_WAKE_SUPPORTED "y")
set(CONFIG_SOC_SDM_GROUPS "1")
set(CONFIG_SOC_SDM_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_SDM_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_SPI_HD_BOTH_INOUT_SUPPORTED "y")
set(CONFIG_SOC_SPI_AS_CS_SUPPORTED "y")
set(CONFIG_SOC_SPI_PERIPH_NUM "3")
set(CONFIG_SOC_SPI_DMA_CHAN_NUM "2")
set(CONFIG_SOC_SPI_MAX_CS_NUM "3")
set(CONFIG_SOC_SPI_SUPPORT_CLK_APB "y")
set(CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE "64")
set(CONFIG_SOC_SPI_MAX_PRE_DIVIDER "8192")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED "y")
set(CONFIG_SOC_TIMER_GROUPS "2")
set(CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP "2")
set(CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH "64")
set(CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS "4")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_APB "y")
set(CONFIG_SOC_TOUCH_SENSOR_VERSION "1")
set(CONFIG_SOC_TOUCH_SENSOR_NUM "10")
set(CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM "1")
set(CONFIG_SOC_TWAI_CONTROLLER_NUM "1")
set(CONFIG_SOC_TWAI_BRP_MIN "2")
set(CONFIG_SOC_TWAI_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT "y")
set(CONFIG_SOC_UART_NUM "3")
set(CONFIG_SOC_UART_HP_NUM "3")
set(CONFIG_SOC_UART_SUPPORT_APB_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_REF_TICK "y")
set(CONFIG_SOC_UART_FIFO_LEN "128")
set(CONFIG_SOC_UART_BITRATE_MAX "5000000")
set(CONFIG_SOC_SPIRAM_SUPPORTED "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE "y")
set(CONFIG_SOC_SHA_SUPPORT_PARALLEL_ENG "y")
set(CONFIG_SOC_SHA_ENDIANNESS_BE "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA1 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA256 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA384 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512 "y")
set(CONFIG_SOC_MPI_MEM_BLOCKS_NUM "4")
set(CONFIG_SOC_MPI_OPERATIONS_NUM "y")
set(CONFIG_SOC_RSA_MAX_BIT_LEN "4096")
set(CONFIG_SOC_AES_SUPPORT_AES_128 "y")
set(CONFIG_SOC_AES_SUPPORT_AES_192 "y")
set(CONFIG_SOC_AES_SUPPORT_AES_256 "y")
set(CONFIG_SOC_SECURE_BOOT_V1 "y")
set(CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS "y")
set(CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX "32")
set(CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE "21")
set(CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_FAST_MEM_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_SLOW_MEM_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RC_FAST_PD "y")
set(CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD "y")
set(CONFIG_SOC_PM_SUPPORT_MODEM_PD "y")
set(CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED "y")
set(CONFIG_SOC_CLK_APLL_SUPPORTED "y")
set(CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256 "y")
set(CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION "y")
set(CONFIG_SOC_CLK_XTAL32K_SUPPORTED "y")
set(CONFIG_SOC_SDMMC_USE_IOMUX "y")
set(CONFIG_SOC_SDMMC_NUM_SLOTS "2")
set(CONFIG_SOC_WIFI_WAPI_SUPPORT "y")
set(CONFIG_SOC_WIFI_CSI_SUPPORT "y")
set(CONFIG_SOC_WIFI_MESH_SUPPORT "y")
set(CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW "y")
set(CONFIG_SOC_WIFI_NAN_SUPPORT "y")
set(CONFIG_SOC_BLE_SUPPORTED "y")
set(CONFIG_SOC_BLE_MESH_SUPPORTED "y")
set(CONFIG_SOC_BT_CLASSIC_SUPPORTED "y")
set(CONFIG_SOC_BLUFI_SUPPORTED "y")
set(CONFIG_SOC_BT_H2C_ENC_KEY_CTRL_ENH_VSC_SUPPORTED "y")
set(CONFIG_SOC_ULP_HAS_ADC "y")
set(CONFIG_SOC_PHY_COMBO_MODULE "y")
set(CONFIG_SOC_EMAC_RMII_CLK_OUT_INTERNAL_LOOPBACK "y")
set(CONFIG_IDF_CMAKE "y")
set(CONFIG_IDF_TOOLCHAIN "gcc")
set(CONFIG_IDF_TARGET_ARCH_XTENSA "y")
set(CONFIG_IDF_TARGET_ARCH "xtensa")
set(CONFIG_IDF_TARGET "esp32")
set(CONFIG_IDF_INIT_VERSION "5.3.1")
set(CONFIG_IDF_TARGET_ESP32 "y")
set(CONFIG_IDF_FIRMWARE_CHIP_ID "0x0")
set(CONFIG_APP_BUILD_TYPE_APP_2NDBOOT "y")
set(CONFIG_APP_BUILD_TYPE_RAM "")
set(CONFIG_APP_BUILD_GENERATE_BINARIES "y")
set(CONFIG_APP_BUILD_BOOTLOADER "y")
set(CONFIG_APP_BUILD_USE_FLASH_SECTIONS "y")
set(CONFIG_APP_REPRODUCIBLE_BUILD "")
set(CONFIG_APP_NO_BLOBS "")
set(CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS "")
set(CONFIG_APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS "")
set(CONFIG_BOOTLOADER_COMPILE_TIME_DATE "y")
set(CONFIG_BOOTLOADER_PROJECT_VER "1")
set(CONFIG_BOOTLOADER_OFFSET_IN_FLASH "0x1000")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE "y")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_ERROR "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_WARN "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_INFO "y")
set(CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL "3")
set(CONFIG_BOOTLOADER_FLASH_DC_AWARE "")
set(CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT "y")
set(CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_8V "")
set(CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V "y")
set(CONFIG_BOOTLOADER_FACTORY_RESET "")
set(CONFIG_BOOTLOADER_APP_TEST "")
set(CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE "")
set(CONFIG_BOOTLOADER_WDT_TIME_MS "9000")
set(CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS "")
set(CONFIG_BOOTLOADER_RESERVE_RTC_SIZE "0x0")
set(CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC "")
set(CONFIG_SECURE_BOOT_V1_SUPPORTED "y")
set(CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT "")
set(CONFIG_SECURE_BOOT "")
set(CONFIG_SECURE_FLASH_ENC_ENABLED "")
set(CONFIG_APP_COMPILE_TIME_DATE "y")
set(CONFIG_APP_EXCLUDE_PROJECT_VER_VAR "")
set(CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR "")
set(CONFIG_APP_PROJECT_VER_FROM_CONFIG "")
set(CONFIG_APP_RETRIEVE_LEN_ELF_SHA "9")
set(CONFIG_ESP_ROM_HAS_CRC_LE "y")
set(CONFIG_ESP_ROM_HAS_CRC_BE "y")
set(CONFIG_ESP_ROM_HAS_MZ_CRC32 "y")
set(CONFIG_ESP_ROM_HAS_JPEG_DECODE "y")
set(CONFIG_ESP_ROM_HAS_UART_BUF_SWITCH "y")
set(CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_32BIT_TIME "y")
set(CONFIG_ESP_ROM_HAS_SW_FLOAT "y")
set(CONFIG_ESP_ROM_USB_OTG_NUM "-1")
set(CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM "-1")
set(CONFIG_ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB "y")
set(CONFIG_ESPTOOLPY_NO_STUB "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QIO "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QOUT "")
set(CONFIG_ESPTOOLPY_FLASHMODE_DIO "y")
set(CONFIG_ESPTOOLPY_FLASHMODE_DOUT "")
set(CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR "y")
set(CONFIG_ESPTOOLPY_FLASHMODE "dio")
set(CONFIG_ESPTOOLPY_FLASHFREQ_80M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_40M "y")
set(CONFIG_ESPTOOLPY_FLASHFREQ_26M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_20M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ "40m")
set(CONFIG_ESPTOOLPY_FLASHSIZE_1MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_2MB "y")
set(CONFIG_ESPTOOLPY_FLASHSIZE_4MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_8MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_16MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_32MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_64MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_128MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE "2MB")
set(CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE "")
set(CONFIG_ESPTOOLPY_BEFORE_RESET "y")
set(CONFIG_ESPTOOLPY_BEFORE_NORESET "")
set(CONFIG_ESPTOOLPY_BEFORE "default_reset")
set(CONFIG_ESPTOOLPY_AFTER_RESET "y")
set(CONFIG_ESPTOOLPY_AFTER_NORESET "")
set(CONFIG_ESPTOOLPY_AFTER "hard_reset")
set(CONFIG_ESPTOOLPY_MONITOR_BAUD "115200")
set(CONFIG_PARTITION_TABLE_SINGLE_APP "y")
set(CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE "")
set(CONFIG_PARTITION_TABLE_TWO_OTA "")
set(CONFIG_PARTITION_TABLE_CUSTOM "")
set(CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv")
set(CONFIG_PARTITION_TABLE_FILENAME "partitions_singleapp.csv")
set(CONFIG_PARTITION_TABLE_OFFSET "0x8000")
set(CONFIG_PARTITION_TABLE_MD5 "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEBUG "")
set(CONFIG_COMPILER_OPTIMIZATION_SIZE "")
set(CONFIG_COMPILER_OPTIMIZATION_PERF "y")
set(CONFIG_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE "")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT "")
set(CONFIG_COMPILER_HIDE_PATHS_MACROS "y")
set(CONFIG_COMPILER_CXX_EXCEPTIONS "")
set(CONFIG_COMPILER_CXX_RTTI "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NONE "y")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NORM "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_STRONG "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_ALL "")
set(CONFIG_COMPILER_WARN_WRITE_STRINGS "")
set(CONFIG_COMPILER_DISABLE_GCC12_WARNINGS "")
set(CONFIG_COMPILER_DISABLE_GCC13_WARNINGS "")
set(CONFIG_COMPILER_DUMP_RTL_FILES "")
set(CONFIG_COMPILER_RT_LIB_GCCLIB "y")
set(CONFIG_COMPILER_RT_LIB_NAME "gcc")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING "")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE "y")
set(CONFIG_EFUSE_CUSTOM_TABLE "")
set(CONFIG_EFUSE_VIRTUAL "")
set(CONFIG_EFUSE_CODE_SCHEME_COMPAT_NONE "")
set(CONFIG_EFUSE_CODE_SCHEME_COMPAT_3_4 "y")
set(CONFIG_EFUSE_CODE_SCHEME_COMPAT_REPEAT "")
set(CONFIG_EFUSE_MAX_BLK_LEN "192")
set(CONFIG_ESP_ERR_TO_NAME_LOOKUP "y")
set(CONFIG_ESP32_REV_MIN_0 "y")
set(CONFIG_ESP32_REV_MIN_1 "")
set(CONFIG_ESP32_REV_MIN_1_1 "")
set(CONFIG_ESP32_REV_MIN_2 "")
set(CONFIG_ESP32_REV_MIN_3 "")
set(CONFIG_ESP32_REV_MIN_3_1 "")
set(CONFIG_ESP32_REV_MIN "0")
set(CONFIG_ESP32_REV_MIN_FULL "0")
set(CONFIG_ESP_REV_MIN_FULL "0")
set(CONFIG_ESP32_REV_MAX_FULL "399")
set(CONFIG_ESP_REV_MAX_FULL "399")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_BT "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_TWO "")
set(CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP_MAC_IGNORE_MAC_CRC_ERROR "")
set(CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC "")
set(CONFIG_ESP_SLEEP_POWER_DOWN_FLASH "")
set(CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU "")
set(CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND "")
set(CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY "2000")
set(CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION "")
set(CONFIG_ESP_SLEEP_DEBUG "")
set(CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS "y")
set(CONFIG_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_PERIPH_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_XTAL_FREQ_26 "")
set(CONFIG_XTAL_FREQ_40 "y")
set(CONFIG_XTAL_FREQ_AUTO "")
set(CONFIG_XTAL_FREQ "40")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240 "y")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ "240")
set(CONFIG_ESP32_USE_FIXED_STATIC_RAM_SIZE "")
set(CONFIG_ESP_SYSTEM_ESP32_SRAM1_REGION_AS_IRAM "")
set(CONFIG_ESP32_TRAX "")
set(CONFIG_ESP32_TRACEMEM_RESERVE_DRAM "0x0")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT "")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT "")
set(CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS "0")
set(CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_ESP_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1 "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY "0x0")
set(CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE "2048")
set(CONFIG_ESP_CONSOLE_UART_DEFAULT "y")
set(CONFIG_ESP_CONSOLE_UART_CUSTOM "")
set(CONFIG_ESP_CONSOLE_NONE "")
set(CONFIG_ESP_CONSOLE_UART "y")
set(CONFIG_ESP_CONSOLE_UART_NUM "0")
set(CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM "0")
set(CONFIG_ESP_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_ESP_INT_WDT "y")
set(CONFIG_ESP_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_ESP_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_ESP_TASK_WDT_EN "y")
set(CONFIG_ESP_TASK_WDT_INIT "y")
set(CONFIG_ESP_TASK_WDT_PANIC "")
set(CONFIG_ESP_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP_PANIC_HANDLER_IRAM "")
set(CONFIG_ESP_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP_DEBUG_OCDAWARE "y")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_5 "")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 "y")
set(CONFIG_ESP_BROWNOUT_DET "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0 "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL "0")
set(CONFIG_ESP32_DISABLE_BASIC_ROM_CONSOLE "")
set(CONFIG_ESP_SYSTEM_BROWNOUT_INTR "y")
set(CONFIG_ESP_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_ESP_IPC_USES_CALLERS_PRIORITY "y")
set(CONFIG_ESP_IPC_ISR_ENABLE "y")
set(CONFIG_FREERTOS_SMP "")
set(CONFIG_FREERTOS_UNICORE "")
set(CONFIG_FREERTOS_HZ "250")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY "y")
set(CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS "1")
set(CONFIG_FREERTOS_IDLE_TASK_STACKSIZE "1024")
set(CONFIG_FREERTOS_USE_IDLE_HOOK "")
set(CONFIG_FREERTOS_USE_TICK_HOOK "")
set(CONFIG_FREERTOS_MAX_TASK_NAME_LEN "16")
set(CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY "")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME "Tmr Svc")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0 "")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU1 "")
set(CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY "y")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_TIMER_TASK_PRIORITY "1")
set(CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_FREERTOS_TIMER_QUEUE_LENGTH "10")
set(CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE "0")
set(CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES "1")
set(CONFIG_FREERTOS_USE_TRACE_FACILITY "")
set(CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES "")
set(CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS "")
set(CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG "")
set(CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK "")
set(CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS "y")
set(CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK "")
set(CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP "")
set(CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER "y")
set(CONFIG_FREERTOS_ISR_STACKSIZE "1536")
set(CONFIG_FREERTOS_INTERRUPT_BACKTRACE "y")
set(CONFIG_FREERTOS_FPU_IN_ISR "")
set(CONFIG_FREERTOS_TICK_SUPPORT_CORETIMER "y")
set(CONFIG_FREERTOS_CORETIMER_0 "y")
set(CONFIG_FREERTOS_CORETIMER_1 "")
set(CONFIG_FREERTOS_SYSTICK_USES_CCOUNT "y")
set(CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE "")
set(CONFIG_FREERTOS_PORT "y")
set(CONFIG_FREERTOS_NO_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION "y")
set(CONFIG_FREERTOS_DEBUG_OCDAWARE "y")
set(CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT "y")
set(CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH "y")
set(CONFIG_FREERTOS_NUMBER_OF_CORES "2")
set(CONFIG_HAL_ASSERTION_EQUALS_SYSTEM "y")
set(CONFIG_HAL_ASSERTION_DISABLE "")
set(CONFIG_HAL_ASSERTION_SILENT "")
set(CONFIG_HAL_ASSERTION_ENABLE "")
set(CONFIG_HAL_DEFAULT_ASSERTION_LEVEL "2")
set(CONFIG_LOG_DEFAULT_LEVEL_NONE "")
set(CONFIG_LOG_DEFAULT_LEVEL_ERROR "")
set(CONFIG_LOG_DEFAULT_LEVEL_WARN "")
set(CONFIG_LOG_DEFAULT_LEVEL_INFO "y")
set(CONFIG_LOG_DEFAULT_LEVEL_DEBUG "")
set(CONFIG_LOG_DEFAULT_LEVEL_VERBOSE "")
set(CONFIG_LOG_DEFAULT_LEVEL "3")
set(CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT "y")
set(CONFIG_LOG_MAXIMUM_LEVEL_DEBUG "")
set(CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE "")
set(CONFIG_LOG_MAXIMUM_LEVEL "3")
set(CONFIG_LOG_MASTER_LEVEL "")
set(CONFIG_LOG_COLORS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_RTOS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF "y")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CR "y")
set(CONFIG_NEWLIB_NANO_FORMAT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE "")
set(CONFIG_MMU_PAGE_SIZE_64KB "y")
set(CONFIG_MMU_PAGE_MODE "64KB")
set(CONFIG_MMU_PAGE_SIZE "0x10000")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC "y")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET "y")
set(CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US "50")
set(CONFIG_SPI_FLASH_VERIFY_WRITE "")
set(CONFIG_SPI_FLASH_ENABLE_COUNTERS "")
set(CONFIG_SPI_FLASH_ROM_DRIVER_PATCH "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED "")
set(CONFIG_SPI_FLASH_SHARE_SPI1_BUS "")
set(CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE "")
set(CONFIG_SPI_FLASH_YIELD_DURING_ERASE "y")
set(CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS "20")
set(CONFIG_SPI_FLASH_ERASE_YIELD_TICKS "1")
set(CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE "8192")
set(CONFIG_SPI_FLASH_SIZE_OVERRIDE "")
set(CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED "")
set(CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST "")
set(CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED "y")
set(CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_GD_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_TH_CHIP "")
set(CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE "y")
set(CONFIG_IDF_EXPERIMENTAL_FEATURES "")
set(CONFIGS_LIST CONFIG_SOC_BROWNOUT_RESET_SUPPORTED;CONFIG_SOC_TWAI_BRP_DIV_SUPPORTED;CONFIG_SOC_DPORT_WORKAROUND;CONFIG_SOC_CAPS_ECO_VER_MAX;CONFIG_SOC_ADC_SUPPORTED;CONFIG_SOC_DAC_SUPPORTED;CONFIG_SOC_UART_SUPPORTED;CONFIG_SOC_MCPWM_SUPPORTED;CONFIG_SOC_GPTIMER_SUPPORTED;CONFIG_SOC_SDMMC_HOST_SUPPORTED;CONFIG_SOC_BT_SUPPORTED;CONFIG_SOC_PCNT_SUPPORTED;CONFIG_SOC_PHY_SUPPORTED;CONFIG_SOC_WIFI_SUPPORTED;CONFIG_SOC_SDIO_SLAVE_SUPPORTED;CONFIG_SOC_TWAI_SUPPORTED;CONFIG_SOC_EFUSE_SUPPORTED;CONFIG_SOC_EMAC_SUPPORTED;CONFIG_SOC_ULP_SUPPORTED;CONFIG_SOC_CCOMP_TIMER_SUPPORTED;CONFIG_SOC_RTC_FAST_MEM_SUPPORTED;CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED;CONFIG_SOC_RTC_MEM_SUPPORTED;CONFIG_SOC_I2S_SUPPORTED;CONFIG_SOC_RMT_SUPPORTED;CONFIG_SOC_SDM_SUPPORTED;CONFIG_SOC_GPSPI_SUPPORTED;CONFIG_SOC_LEDC_SUPPORTED;CONFIG_SOC_I2C_SUPPORTED;CONFIG_SOC_SUPPORT_COEXISTENCE;CONFIG_SOC_AES_SUPPORTED;CONFIG_SOC_MPI_SUPPORTED;CONFIG_SOC_SHA_SUPPORTED;CONFIG_SOC_FLASH_ENC_SUPPORTED;CONFIG_SOC_SECURE_BOOT_SUPPORTED;CONFIG_SOC_TOUCH_SENSOR_SUPPORTED;CONFIG_SOC_BOD_SUPPORTED;CONFIG_SOC_ULP_FSM_SUPPORTED;CONFIG_SOC_CLK_TREE_SUPPORTED;CONFIG_SOC_MPU_SUPPORTED;CONFIG_SOC_WDT_SUPPORTED;CONFIG_SOC_SPI_FLASH_SUPPORTED;CONFIG_SOC_RNG_SUPPORTED;CONFIG_SOC_LIGHT_SLEEP_SUPPORTED;CONFIG_SOC_DEEP_SLEEP_SUPPORTED;CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT;CONFIG_SOC_PM_SUPPORTED;CONFIG_SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL;CONFIG_SOC_XTAL_SUPPORT_26M;CONFIG_SOC_XTAL_SUPPORT_40M;CONFIG_SOC_XTAL_SUPPORT_AUTO_DETECT;CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED;CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED;CONFIG_SOC_ADC_DMA_SUPPORTED;CONFIG_SOC_ADC_PERIPH_NUM;CONFIG_SOC_ADC_MAX_CHANNEL_NUM;CONFIG_SOC_ADC_ATTEN_NUM;CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM;CONFIG_SOC_ADC_PATT_LEN_MAX;CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH;CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH;CONFIG_SOC_ADC_DIGI_RESULT_BYTES;CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV;CONFIG_SOC_ADC_DIGI_MONITOR_NUM;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW;CONFIG_SOC_ADC_RTC_MIN_BITWIDTH;CONFIG_SOC_ADC_RTC_MAX_BITWIDTH;CONFIG_SOC_ADC_SHARED_POWER;CONFIG_SOC_SHARED_IDCACHE_SUPPORTED;CONFIG_SOC_IDCACHE_PER_CORE;CONFIG_SOC_CPU_CORES_NUM;CONFIG_SOC_CPU_INTR_NUM;CONFIG_SOC_CPU_HAS_FPU;CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES;CONFIG_SOC_CPU_BREAKPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE;CONFIG_SOC_DAC_CHAN_NUM;CONFIG_SOC_DAC_RESOLUTION;CONFIG_SOC_DAC_DMA_16BIT_ALIGN;CONFIG_SOC_GPIO_PORT;CONFIG_SOC_GPIO_PIN_COUNT;CONFIG_SOC_GPIO_VALID_GPIO_MASK;CONFIG_SOC_GPIO_IN_RANGE_MAX;CONFIG_SOC_GPIO_OUT_RANGE_MAX;CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK;CONFIG_SOC_GPIO_CLOCKOUT_BY_IO_MUX;CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM;CONFIG_SOC_I2C_NUM;CONFIG_SOC_HP_I2C_NUM;CONFIG_SOC_I2C_FIFO_LEN;CONFIG_SOC_I2C_CMD_REG_NUM;CONFIG_SOC_I2C_SUPPORT_SLAVE;CONFIG_SOC_I2C_SUPPORT_APB;CONFIG_SOC_I2C_STOP_INDEPENDENT;CONFIG_SOC_I2S_NUM;CONFIG_SOC_I2S_HW_VERSION_1;CONFIG_SOC_I2S_SUPPORTS_APLL;CONFIG_SOC_I2S_SUPPORTS_PLL_F160M;CONFIG_SOC_I2S_SUPPORTS_PDM;CONFIG_SOC_I2S_SUPPORTS_PDM_TX;CONFIG_SOC_I2S_PDM_MAX_TX_LINES;CONFIG_SOC_I2S_SUPPORTS_PDM_RX;CONFIG_SOC_I2S_PDM_MAX_RX_LINES;CONFIG_SOC_I2S_SUPPORTS_ADC_DAC;CONFIG_SOC_I2S_SUPPORTS_ADC;CONFIG_SOC_I2S_SUPPORTS_DAC;CONFIG_SOC_I2S_SUPPORTS_LCD_CAMERA;CONFIG_SOC_I2S_TRANS_SIZE_ALIGN_WORD;CONFIG_SOC_I2S_LCD_I80_VARIANT;CONFIG_SOC_LCD_I80_SUPPORTED;CONFIG_SOC_LCD_I80_BUSES;CONFIG_SOC_LCD_I80_BUS_WIDTH;CONFIG_SOC_LEDC_HAS_TIMER_SPECIFIC_MUX;CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK;CONFIG_SOC_LEDC_SUPPORT_REF_TICK;CONFIG_SOC_LEDC_SUPPORT_HS_MODE;CONFIG_SOC_LEDC_CHANNEL_NUM;CONFIG_SOC_LEDC_TIMER_BIT_WIDTH;CONFIG_SOC_MCPWM_GROUPS;CONFIG_SOC_MCPWM_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP;CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR;CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER;CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP;CONFIG_SOC_MMU_PERIPH_NUM;CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM;CONFIG_SOC_MPU_MIN_REGION_SIZE;CONFIG_SOC_MPU_REGIONS_MAX_NUM;CONFIG_SOC_PCNT_GROUPS;CONFIG_SOC_PCNT_UNITS_PER_GROUP;CONFIG_SOC_PCNT_CHANNELS_PER_UNIT;CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT;CONFIG_SOC_RMT_GROUPS;CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_CHANNELS_PER_GROUP;CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL;CONFIG_SOC_RMT_SUPPORT_REF_TICK;CONFIG_SOC_RMT_SUPPORT_APB;CONFIG_SOC_RMT_CHANNEL_CLK_INDEPENDENT;CONFIG_SOC_RTCIO_PIN_COUNT;CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED;CONFIG_SOC_RTCIO_HOLD_SUPPORTED;CONFIG_SOC_RTCIO_WAKE_SUPPORTED;CONFIG_SOC_SDM_GROUPS;CONFIG_SOC_SDM_CHANNELS_PER_GROUP;CONFIG_SOC_SDM_CLK_SUPPORT_APB;CONFIG_SOC_SPI_HD_BOTH_INOUT_SUPPORTED;CONFIG_SOC_SPI_AS_CS_SUPPORTED;CONFIG_SOC_SPI_PERIPH_NUM;CONFIG_SOC_SPI_DMA_CHAN_NUM;CONFIG_SOC_SPI_MAX_CS_NUM;CONFIG_SOC_SPI_SUPPORT_CLK_APB;CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE;CONFIG_SOC_SPI_MAX_PRE_DIVIDER;CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED;CONFIG_SOC_TIMER_GROUPS;CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP;CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH;CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS;CONFIG_SOC_TIMER_GROUP_SUPPORT_APB;CONFIG_SOC_TOUCH_SENSOR_VERSION;CONFIG_SOC_TOUCH_SENSOR_NUM;CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM;CONFIG_SOC_TWAI_CONTROLLER_NUM;CONFIG_SOC_TWAI_BRP_MIN;CONFIG_SOC_TWAI_CLK_SUPPORT_APB;CONFIG_SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT;CONFIG_SOC_UART_NUM;CONFIG_SOC_UART_HP_NUM;CONFIG_SOC_UART_SUPPORT_APB_CLK;CONFIG_SOC_UART_SUPPORT_REF_TICK;CONFIG_SOC_UART_FIFO_LEN;CONFIG_SOC_UART_BITRATE_MAX;CONFIG_SOC_SPIRAM_SUPPORTED;CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE;CONFIG_SOC_SHA_SUPPORT_PARALLEL_ENG;CONFIG_SOC_SHA_ENDIANNESS_BE;CONFIG_SOC_SHA_SUPPORT_SHA1;CONFIG_SOC_SHA_SUPPORT_SHA256;CONFIG_SOC_SHA_SUPPORT_SHA384;CONFIG_SOC_SHA_SUPPORT_SHA512;CONFIG_SOC_MPI_MEM_BLOCKS_NUM;CONFIG_SOC_MPI_OPERATIONS_NUM;CONFIG_SOC_RSA_MAX_BIT_LEN;CONFIG_SOC_AES_SUPPORT_AES_128;CONFIG_SOC_AES_SUPPORT_AES_192;CONFIG_SOC_AES_SUPPORT_AES_256;CONFIG_SOC_SECURE_BOOT_V1;CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS;CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX;CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE;CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP;CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP;CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD;CONFIG_SOC_PM_SUPPORT_RTC_FAST_MEM_PD;CONFIG_SOC_PM_SUPPORT_RTC_SLOW_MEM_PD;CONFIG_SOC_PM_SUPPORT_RC_FAST_PD;CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD;CONFIG_SOC_PM_SUPPORT_MODEM_PD;CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED;CONFIG_SOC_CLK_APLL_SUPPORTED;CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED;CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256;CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION;CONFIG_SOC_CLK_XTAL32K_SUPPORTED;CONFIG_SOC_SDMMC_USE_IOMUX;CONFIG_SOC_SDMMC_NUM_SLOTS;CONFIG_SOC_WIFI_WAPI_SUPPORT;CONFIG_SOC_WIFI_CSI_SUPPORT;CONFIG_SOC_WIFI_MESH_SUPPORT;CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW;CONFIG_SOC_WIFI_NAN_SUPPORT;CONFIG_SOC_BLE_SUPPORTED;CONFIG_SOC_BLE_MESH_SUPPORTED;CONFIG_SOC_BT_CLASSIC_SUPPORTED;CONFIG_SOC_BLUFI_SUPPORTED;CONFIG_SOC_BT_H2C_ENC_KEY_CTRL_ENH_VSC_SUPPORTED;CONFIG_SOC_ULP_HAS_ADC;CONFIG_SOC_PHY_COMBO_MODULE;CONFIG_SOC_EMAC_RMII_CLK_OUT_INTERNAL_LOOPBACK;CONFIG_IDF_CMAKE;CONFIG_IDF_TOOLCHAIN;CONFIG_IDF_TARGET_ARCH_XTENSA;CONFIG_IDF_TARGET_ARCH;CONFIG_IDF_TARGET;CONFIG_IDF_INIT_VERSION;CONFIG_IDF_TARGET_ESP32;CONFIG_IDF_FIRMWARE_CHIP_ID;CONFIG_APP_BUILD_TYPE_APP_2NDBOOT;CONFIG_APP_BUILD_TYPE_RAM;CONFIG_APP_BUILD_TYPE_ELF_RAM;CONFIG_APP_BUILD_GENERATE_BINARIES;CONFIG_APP_BUILD_BOOTLOADER;CONFIG_APP_BUILD_USE_FLASH_SECTIONS;CONFIG_APP_REPRODUCIBLE_BUILD;CONFIG_APP_NO_BLOBS;CONFIG_NO_BLOBS;CONFIG_ESP32_NO_BLOBS;CONFIG_APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS;CONFIG_ESP32_COMPATIBLE_PRE_V2_1_BOOTLOADERS;CONFIG_APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS;CONFIG_ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS;CONFIG_BOOTLOADER_COMPILE_TIME_DATE;CONFIG_BOOTLOADER_PROJECT_VER;CONFIG_BOOTLOADER_OFFSET_IN_FLASH;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_NONE;CONFIG_LOG_BOOTLOADER_LEVEL_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_ERROR;CONFIG_LOG_BOOTLOADER_LEVEL_ERROR;CONFIG_BOOTLOADER_LOG_LEVEL_WARN;CONFIG_LOG_BOOTLOADER_LEVEL_WARN;CONFIG_BOOTLOADER_LOG_LEVEL_INFO;CONFIG_LOG_BOOTLOADER_LEVEL_INFO;CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG;CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG;CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE;CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE;CONFIG_BOOTLOADER_LOG_LEVEL;CONFIG_LOG_BOOTLOADER_LEVEL;CONFIG_BOOTLOADER_FLASH_DC_AWARE;CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT;CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_8V;CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V;CONFIG_BOOTLOADER_FACTORY_RESET;CONFIG_BOOTLOADER_APP_TEST;CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE;CONFIG_BOOTLOADER_WDT_ENABLE;CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE;CONFIG_BOOTLOADER_WDT_TIME_MS;CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE;CONFIG_APP_ROLLBACK_ENABLE;CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP;CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON;CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS;CONFIG_BOOTLOADER_RESERVE_RTC_SIZE;CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC;CONFIG_SECURE_BOOT_V1_SUPPORTED;CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT;CONFIG_SECURE_BOOT;CONFIG_SECURE_FLASH_ENC_ENABLED;CONFIG_FLASH_ENCRYPTION_ENABLED;CONFIG_APP_COMPILE_TIME_DATE;CONFIG_APP_EXCLUDE_PROJECT_VER_VAR;CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR;CONFIG_APP_PROJECT_VER_FROM_CONFIG;CONFIG_APP_RETRIEVE_LEN_ELF_SHA;CONFIG_ESP_ROM_HAS_CRC_LE;CONFIG_ESP_ROM_HAS_CRC_BE;CONFIG_ESP_ROM_HAS_MZ_CRC32;CONFIG_ESP_ROM_HAS_JPEG_DECODE;CONFIG_ESP_ROM_HAS_UART_BUF_SWITCH;CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND;CONFIG_ESP_ROM_HAS_NEWLIB;CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT;CONFIG_ESP_ROM_HAS_NEWLIB_32BIT_TIME;CONFIG_ESP_ROM_HAS_SW_FLOAT;CONFIG_ESP_ROM_USB_OTG_NUM;CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM;CONFIG_ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB;CONFIG_ESPTOOLPY_NO_STUB;CONFIG_ESPTOOLPY_FLASHMODE_QIO;CONFIG_FLASHMODE_QIO;CONFIG_ESPTOOLPY_FLASHMODE_QOUT;CONFIG_FLASHMODE_QOUT;CONFIG_ESPTOOLPY_FLASHMODE_DIO;CONFIG_FLASHMODE_DIO;CONFIG_ESPTOOLPY_FLASHMODE_DOUT;CONFIG_FLASHMODE_DOUT;CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR;CONFIG_ESPTOOLPY_FLASHMODE;CONFIG_ESPTOOLPY_FLASHFREQ_80M;CONFIG_ESPTOOLPY_FLASHFREQ_40M;CONFIG_ESPTOOLPY_FLASHFREQ_26M;CONFIG_ESPTOOLPY_FLASHFREQ_20M;CONFIG_ESPTOOLPY_FLASHFREQ;CONFIG_ESPTOOLPY_FLASHSIZE_1MB;CONFIG_ESPTOOLPY_FLASHSIZE_2MB;CONFIG_ESPTOOLPY_FLASHSIZE_4MB;CONFIG_ESPTOOLPY_FLASHSIZE_8MB;CONFIG_ESPTOOLPY_FLASHSIZE_16MB;CONFIG_ESPTOOLPY_FLASHSIZE_32MB;CONFIG_ESPTOOLPY_FLASHSIZE_64MB;CONFIG_ESPTOOLPY_FLASHSIZE_128MB;CONFIG_ESPTOOLPY_FLASHSIZE;CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE;CONFIG_ESPTOOLPY_BEFORE_RESET;CONFIG_ESPTOOLPY_BEFORE_NORESET;CONFIG_ESPTOOLPY_BEFORE;CONFIG_ESPTOOLPY_AFTER_RESET;CONFIG_ESPTOOLPY_AFTER_NORESET;CONFIG_ESPTOOLPY_AFTER;CONFIG_ESPTOOLPY_MONITOR_BAUD;CONFIG_MONITOR_BAUD;CONFIG_PARTITION_TABLE_SINGLE_APP;CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE;CONFIG_PARTITION_TABLE_TWO_OTA;CONFIG_PARTITION_TABLE_CUSTOM;CONFIG_PARTITION_TABLE_CUSTOM_FILENAME;CONFIG_PARTITION_TABLE_FILENAME;CONFIG_PARTITION_TABLE_OFFSET;CONFIG_PARTITION_TABLE_MD5;CONFIG_COMPILER_OPTIMIZATION_DEBUG;CONFIG_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_DEFAULT;CONFIG_COMPILER_OPTIMIZATION_SIZE;CONFIG_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_PERF;CONFIG_COMPILER_OPTIMIZATION_NONE;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE;CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE;CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED;CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB;CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT;CONFIG_COMPILER_HIDE_PATHS_MACROS;CONFIG_COMPILER_CXX_EXCEPTIONS;CONFIG_CXX_EXCEPTIONS;CONFIG_COMPILER_CXX_RTTI;CONFIG_COMPILER_STACK_CHECK_MODE_NONE;CONFIG_STACK_CHECK_NONE;CONFIG_COMPILER_STACK_CHECK_MODE_NORM;CONFIG_STACK_CHECK_NORM;CONFIG_COMPILER_STACK_CHECK_MODE_STRONG;CONFIG_STACK_CHECK_STRONG;CONFIG_COMPILER_STACK_CHECK_MODE_ALL;CONFIG_STACK_CHECK_ALL;CONFIG_COMPILER_WARN_WRITE_STRINGS;CONFIG_WARN_WRITE_STRINGS;CONFIG_COMPILER_DISABLE_GCC12_WARNINGS;CONFIG_COMPILER_DISABLE_GCC13_WARNINGS;CONFIG_COMPILER_DUMP_RTL_FILES;CONFIG_COMPILER_RT_LIB_GCCLIB;CONFIG_COMPILER_RT_LIB_NAME;CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING;CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE;CONFIG_EFUSE_CUSTOM_TABLE;CONFIG_EFUSE_VIRTUAL;CONFIG_EFUSE_CODE_SCHEME_COMPAT_NONE;CONFIG_EFUSE_CODE_SCHEME_COMPAT_3_4;CONFIG_EFUSE_CODE_SCHEME_COMPAT_REPEAT;CONFIG_EFUSE_MAX_BLK_LEN;CONFIG_ESP_ERR_TO_NAME_LOOKUP;CONFIG_ESP32_REV_MIN_0;CONFIG_ESP32_REV_MIN_1;CONFIG_ESP32_REV_MIN_1_1;CONFIG_ESP32_REV_MIN_2;CONFIG_ESP32_REV_MIN_3;CONFIG_ESP32_REV_MIN_3_1;CONFIG_ESP32_REV_MIN;CONFIG_ESP32_REV_MIN_FULL;CONFIG_ESP_REV_MIN_FULL;CONFIG_ESP32_REV_MAX_FULL;CONFIG_ESP_REV_MAX_FULL;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP;CONFIG_ESP_MAC_ADDR_UNIVERSE_BT;CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_TWO;CONFIG_TWO_UNIVERSAL_MAC_ADDRESS;CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_FOUR_UNIVERSAL_MAC_ADDRESS;CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES;CONFIG_NUMBER_OF_UNIVERSAL_MAC_ADDRESS;CONFIG_ESP_MAC_IGNORE_MAC_CRC_ERROR;CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC;CONFIG_ESP_SLEEP_POWER_DOWN_FLASH;CONFIG_ESP_SYSTEM_PD_FLASH;CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND;CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU;CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND;CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND;CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY;CONFIG_ESP32_DEEP_SLEEP_WAKEUP_DELAY;CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY;CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION;CONFIG_ESP_SLEEP_DEBUG;CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS;CONFIG_RTC_CLK_SRC_INT_RC;CONFIG_ESP32_RTC_CLK_SRC_INT_RC;CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_RC;CONFIG_RTC_CLK_SRC_EXT_CRYS;CONFIG_ESP32_RTC_CLK_SRC_EXT_CRYS;CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_CRYSTAL;CONFIG_RTC_CLK_SRC_EXT_OSC;CONFIG_ESP32_RTC_CLK_SRC_EXT_OSC;CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_OSC;CONFIG_RTC_CLK_SRC_INT_8MD256;CONFIG_ESP32_RTC_CLK_SRC_INT_8MD256;CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_8MD256;CONFIG_RTC_CLK_CAL_CYCLES;CONFIG_ESP32_RTC_CLK_CAL_CYCLES;CONFIG_PERIPH_CTRL_FUNC_IN_IRAM;CONFIG_XTAL_FREQ_26;CONFIG_ESP32_XTAL_FREQ_26;CONFIG_XTAL_FREQ_40;CONFIG_ESP32_XTAL_FREQ_40;CONFIG_XTAL_FREQ_AUTO;CONFIG_ESP32_XTAL_FREQ_AUTO;CONFIG_XTAL_FREQ;CONFIG_ESP32_XTAL_FREQ;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80;CONFIG_ESP32_DEFAULT_CPU_FREQ_80;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160;CONFIG_ESP32_DEFAULT_CPU_FREQ_160;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240;CONFIG_ESP32_DEFAULT_CPU_FREQ_240;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32_USE_FIXED_STATIC_RAM_SIZE;CONFIG_ESP_SYSTEM_ESP32_SRAM1_REGION_AS_IRAM;CONFIG_ESP32_TRAX;CONFIG_ESP32_TRACEMEM_RESERVE_DRAM;CONFIG_TRACEMEM_RESERVE_DRAM;CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT;CONFIG_ESP32_PANIC_PRINT_HALT;CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT;CONFIG_ESP32_PANIC_PRINT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT;CONFIG_ESP32_PANIC_SILENT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS;CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_STACK_SIZE;CONFIG_MAIN_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1;CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY;CONFIG_ESP_MAIN_TASK_AFFINITY;CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE;CONFIG_ESP_CONSOLE_UART_DEFAULT;CONFIG_CONSOLE_UART_DEFAULT;CONFIG_ESP_CONSOLE_UART_CUSTOM;CONFIG_CONSOLE_UART_CUSTOM;CONFIG_ESP_CONSOLE_NONE;CONFIG_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART;CONFIG_CONSOLE_UART;CONFIG_ESP_CONSOLE_UART_NUM;CONFIG_CONSOLE_UART_NUM;CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM;CONFIG_ESP_CONSOLE_UART_BAUDRATE;CONFIG_CONSOLE_UART_BAUDRATE;CONFIG_ESP_INT_WDT;CONFIG_INT_WDT;CONFIG_ESP_INT_WDT_TIMEOUT_MS;CONFIG_INT_WDT_TIMEOUT_MS;CONFIG_ESP_INT_WDT_CHECK_CPU1;CONFIG_INT_WDT_CHECK_CPU1;CONFIG_ESP_TASK_WDT_EN;CONFIG_ESP_TASK_WDT_INIT;CONFIG_TASK_WDT;CONFIG_ESP_TASK_WDT;CONFIG_ESP_TASK_WDT_PANIC;CONFIG_TASK_WDT_PANIC;CONFIG_ESP_TASK_WDT_TIMEOUT_S;CONFIG_TASK_WDT_TIMEOUT_S;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_ESP_PANIC_HANDLER_IRAM;CONFIG_ESP_DEBUG_STUBS_ENABLE;CONFIG_ESP32_DEBUG_STUBS_ENABLE;CONFIG_ESP_DEBUG_OCDAWARE;CONFIG_ESP32_DEBUG_OCDAWARE;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_5;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4;CONFIG_ESP_BROWNOUT_DET;CONFIG_BROWNOUT_DET;CONFIG_ESP32_BROWNOUT_DET;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0;CONFIG_BROWNOUT_DET_LVL_SEL_0;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_0;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_1;CONFIG_BROWNOUT_DET_LVL_SEL_1;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_1;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2;CONFIG_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3;CONFIG_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4;CONFIG_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5;CONFIG_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6;CONFIG_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7;CONFIG_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP_BROWNOUT_DET_LVL;CONFIG_BROWNOUT_DET_LVL;CONFIG_ESP32_BROWNOUT_DET_LVL;CONFIG_ESP32_DISABLE_BASIC_ROM_CONSOLE;CONFIG_DISABLE_BASIC_ROM_CONSOLE;CONFIG_ESP_SYSTEM_BROWNOUT_INTR;CONFIG_ESP_IPC_TASK_STACK_SIZE;CONFIG_IPC_TASK_STACK_SIZE;CONFIG_ESP_IPC_USES_CALLERS_PRIORITY;CONFIG_ESP_IPC_ISR_ENABLE;CONFIG_FREERTOS_SMP;CONFIG_FREERTOS_UNICORE;CONFIG_FREERTOS_HZ;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY;CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS;CONFIG_FREERTOS_IDLE_TASK_STACKSIZE;CONFIG_FREERTOS_USE_IDLE_HOOK;CONFIG_FREERTOS_USE_TICK_HOOK;CONFIG_FREERTOS_MAX_TASK_NAME_LEN;CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU1;CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY;CONFIG_FREERTOS_TIMER_TASK_PRIORITY;CONFIG_TIMER_TASK_PRIORITY;CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH;CONFIG_TIMER_TASK_STACK_DEPTH;CONFIG_FREERTOS_TIMER_QUEUE_LENGTH;CONFIG_TIMER_QUEUE_LENGTH;CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE;CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES;CONFIG_FREERTOS_USE_TRACE_FACILITY;CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES;CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS;CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG;CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK;CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS;CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK;CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP;CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK;CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER;CONFIG_FREERTOS_ISR_STACKSIZE;CONFIG_FREERTOS_INTERRUPT_BACKTRACE;CONFIG_FREERTOS_FPU_IN_ISR;CONFIG_FREERTOS_TICK_SUPPORT_CORETIMER;CONFIG_FREERTOS_CORETIMER_0;CONFIG_FREERTOS_CORETIMER_1;CONFIG_FREERTOS_SYSTICK_USES_CCOUNT;CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE;CONFIG_FREERTOS_PORT;CONFIG_FREERTOS_NO_AFFINITY;CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION;CONFIG_FREERTOS_DEBUG_OCDAWARE;CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT;CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH;CONFIG_FREERTOS_NUMBER_OF_CORES;CONFIG_HAL_ASSERTION_EQUALS_SYSTEM;CONFIG_HAL_ASSERTION_DISABLE;CONFIG_HAL_ASSERTION_SILENT;CONFIG_HAL_ASSERTION_SILIENT;CONFIG_HAL_ASSERTION_ENABLE;CONFIG_HAL_DEFAULT_ASSERTION_LEVEL;CONFIG_LOG_DEFAULT_LEVEL_NONE;CONFIG_LOG_DEFAULT_LEVEL_ERROR;CONFIG_LOG_DEFAULT_LEVEL_WARN;CONFIG_LOG_DEFAULT_LEVEL_INFO;CONFIG_LOG_DEFAULT_LEVEL_DEBUG;CONFIG_LOG_DEFAULT_LEVEL_VERBOSE;CONFIG_LOG_DEFAULT_LEVEL;CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT;CONFIG_LOG_MAXIMUM_LEVEL_DEBUG;CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE;CONFIG_LOG_MAXIMUM_LEVEL;CONFIG_LOG_MASTER_LEVEL;CONFIG_LOG_COLORS;CONFIG_LOG_TIMESTAMP_SOURCE_RTOS;CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR;CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDIN_LINE_ENDING_LF;CONFIG_NEWLIB_STDIN_LINE_ENDING_CR;CONFIG_NEWLIB_NANO_FORMAT;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_RTC_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_RTC_FRC1;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC;CONFIG_ESP32_TIME_SYSCALL_USE_RTC;CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_HRT;CONFIG_ESP32_TIME_SYSCALL_USE_FRC1;CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE;CONFIG_ESP32_TIME_SYSCALL_USE_NONE;CONFIG_MMU_PAGE_SIZE_64KB;CONFIG_MMU_PAGE_MODE;CONFIG_MMU_PAGE_SIZE;CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC;CONFIG_SPI_FLASH_BROWNOUT_RESET;CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US;CONFIG_SPI_FLASH_VERIFY_WRITE;CONFIG_SPI_FLASH_ENABLE_COUNTERS;CONFIG_SPI_FLASH_ROM_DRIVER_PATCH;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED;CONFIG_SPI_FLASH_SHARE_SPI1_BUS;CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE;CONFIG_SPI_FLASH_YIELD_DURING_ERASE;CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS;CONFIG_SPI_FLASH_ERASE_YIELD_TICKS;CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE;CONFIG_SPI_FLASH_SIZE_OVERRIDE;CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED;CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST;CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED;CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP;CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP;CONFIG_SPI_FLASH_SUPPORT_GD_CHIP;CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP;CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP;CONFIG_SPI_FLASH_SUPPORT_TH_CHIP;CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE;CONFIG_IDF_EXPERIMENTAL_FEATURES)
# List of deprecated options for backward compatibility
set(CONFIG_APP_BUILD_TYPE_ELF_RAM "")
set(CONFIG_NO_BLOBS "")
set(CONFIG_ESP32_NO_BLOBS "")
set(CONFIG_ESP32_COMPATIBLE_PRE_V2_1_BOOTLOADERS "")
set(CONFIG_ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_NONE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_ERROR "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_WARN "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_INFO "y")
set(CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL "3")
set(CONFIG_APP_ROLLBACK_ENABLE "")
set(CONFIG_FLASH_ENCRYPTION_ENABLED "")
set(CONFIG_FLASHMODE_QIO "")
set(CONFIG_FLASHMODE_QOUT "")
set(CONFIG_FLASHMODE_DIO "y")
set(CONFIG_FLASHMODE_DOUT "")
set(CONFIG_MONITOR_BAUD "115200")
set(CONFIG_OPTIMIZATION_LEVEL_DEBUG "")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG "")
set(CONFIG_COMPILER_OPTIMIZATION_DEFAULT "")
set(CONFIG_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED "y")
set(CONFIG_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED "")
set(CONFIG_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_CXX_EXCEPTIONS "")
set(CONFIG_STACK_CHECK_NONE "y")
set(CONFIG_STACK_CHECK_NORM "")
set(CONFIG_STACK_CHECK_STRONG "")
set(CONFIG_STACK_CHECK_ALL "")
set(CONFIG_WARN_WRITE_STRINGS "")
set(CONFIG_TWO_UNIVERSAL_MAC_ADDRESS "")
set(CONFIG_FOUR_UNIVERSAL_MAC_ADDRESS "y")
set(CONFIG_NUMBER_OF_UNIVERSAL_MAC_ADDRESS "4")
set(CONFIG_ESP_SYSTEM_PD_FLASH "")
set(CONFIG_ESP32_DEEP_SLEEP_WAKEUP_DELAY "2000")
set(CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY "2000")
set(CONFIG_ESP32_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_RC "y")
set(CONFIG_ESP32_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_CRYSTAL "")
set(CONFIG_ESP32_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_EXTERNAL_OSC "")
set(CONFIG_ESP32_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_8MD256 "")
set(CONFIG_ESP32_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_ESP32_XTAL_FREQ_26 "")
set(CONFIG_ESP32_XTAL_FREQ_40 "y")
set(CONFIG_ESP32_XTAL_FREQ_AUTO "")
set(CONFIG_ESP32_XTAL_FREQ "40")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_80 "")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_160 "")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_240 "y")
set(CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ "240")
set(CONFIG_TRACEMEM_RESERVE_DRAM "0x0")
set(CONFIG_ESP32_PANIC_PRINT_HALT "")
set(CONFIG_ESP32_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP32_PANIC_SILENT_REBOOT "")
set(CONFIG_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_CONSOLE_UART_DEFAULT "y")
set(CONFIG_CONSOLE_UART_CUSTOM "")
set(CONFIG_CONSOLE_UART_NONE "")
set(CONFIG_ESP_CONSOLE_UART_NONE "")
set(CONFIG_CONSOLE_UART "y")
set(CONFIG_CONSOLE_UART_NUM "0")
set(CONFIG_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_INT_WDT "y")
set(CONFIG_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_TASK_WDT "y")
set(CONFIG_ESP_TASK_WDT "y")
set(CONFIG_TASK_WDT_PANIC "")
set(CONFIG_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP32_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP32_DEBUG_OCDAWARE "y")
set(CONFIG_BROWNOUT_DET "y")
set(CONFIG_ESP32_BROWNOUT_DET "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_0 "y")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_0 "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_1 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_7 "")
set(CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_7 "")
set(CONFIG_BROWNOUT_DET_LVL "0")
set(CONFIG_ESP32_BROWNOUT_DET_LVL "0")
set(CONFIG_DISABLE_BASIC_ROM_CONSOLE "")
set(CONFIG_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_TIMER_TASK_PRIORITY "1")
set(CONFIG_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_TIMER_QUEUE_LENGTH "10")
set(CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK "")
set(CONFIG_HAL_ASSERTION_SILIENT "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_ESP32_TIME_SYSCALL_USE_RTC_FRC1 "y")
set(CONFIG_ESP32_TIME_SYSCALL_USE_RTC "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_HRT "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_FRC1 "")
set(CONFIG_ESP32_TIME_SYSCALL_USE_NONE "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS "y")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED "")
