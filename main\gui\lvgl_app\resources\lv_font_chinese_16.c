﻿/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts:
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_FONT_CHINESE_16
#define LV_FONT_CHINESE_16 1
#endif

#if LV_FONT_CHINESE_16

 /*-----------------
  *    BITMAPS
  *----------------*/

  /*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xfb, 0xea, 0xea, 0xd9, 0xc8, 0xb7, 0xa6, 0xa6,
    0x85, 0x0, 0xfb, 0xfc,

    /* U+002D "-" */
    0xaf, 0xff, 0xff, 0xf6, 0x12, 0x22, 0x22, 0x20,

    /* U+002E "." */
    0x55, 0xcb,

    /* U+4E2D "中" */
    0x0, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0x1, 0xee, 0xee, 0xef, 0xfe,
    0xee, 0xe9, 0x1f, 0x51, 0x11, 0xe7, 0x11, 0x1b,
    0xa1, 0xf4, 0x0, 0xe, 0x60, 0x0, 0xba, 0x1f,
    0x40, 0x0, 0xe6, 0x0, 0xb, 0xa1, 0xf4, 0x0,
    0xe, 0x60, 0x0, 0xba, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa1, 0xb2, 0x0, 0xe, 0x60, 0x0,
    0x77, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf7, 0x0,
    0x0, 0x0,

    /* U+4E8E "于" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x22, 0x22, 0x2e, 0x72, 0x22, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x60, 0x0, 0x0, 0x1, 0x33, 0x33,
    0x33, 0xe8, 0x33, 0x33, 0x33, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xaf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xae, 0xb1, 0x0, 0x0,
    0x0,

    /* U+5173 "关" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0x70, 0x0, 0x3d, 0x20, 0x0, 0x0, 0x5,
    0xf3, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x0, 0xb6,
    0x0, 0xf5, 0x0, 0x0, 0xc, 0xcc, 0xcc, 0xcd,
    0xfc, 0xcc, 0x80, 0x5, 0x44, 0x44, 0xe7, 0x44,
    0x44, 0x30, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x11, 0xe6, 0x22, 0x22, 0x31, 0xae,
    0xee, 0xee, 0xff, 0xee, 0xee, 0xe7, 0x0, 0x0,
    0x5, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0x8b, 0xc1, 0x0, 0x0, 0x0, 0x1, 0xcc, 0x1,
    0xdd, 0x30, 0x0, 0x0, 0x6e, 0xb1, 0x0, 0x1c,
    0xfb, 0x51, 0x7e, 0xe6, 0x0, 0x0, 0x0, 0x7e,
    0xf7, 0x27, 0x0, 0x0, 0x0, 0x0, 0x0, 0x50,

    /* U+5206 "分" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x30, 0x1d, 0x20, 0x0, 0x0, 0x0,
    0x2, 0xf6, 0x0, 0xca, 0x0, 0x0, 0x0, 0x0,
    0x9d, 0x0, 0x3, 0xf5, 0x0, 0x0, 0x0, 0x3f,
    0x50, 0x0, 0x9, 0xf2, 0x0, 0x0, 0xd, 0xa0,
    0x0, 0x0, 0xc, 0xf4, 0x0, 0xb, 0xe1, 0x0,
    0x0, 0x0, 0xb, 0xf9, 0x1a, 0xf7, 0xff, 0xff,
    0xff, 0xff, 0x78, 0xd1, 0x23, 0x14, 0x4f, 0x73,
    0x33, 0xe6, 0x0, 0x0, 0x0, 0x2, 0xf2, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x1,
    0xf2, 0x0, 0x0, 0x0, 0xd, 0x90, 0x0, 0x3f,
    0x0, 0x0, 0x0, 0xb, 0xe1, 0x0, 0x5, 0xf0,
    0x0, 0x0, 0x4c, 0xe2, 0x0, 0x37, 0xdb, 0x0,
    0x0, 0x3f, 0xb1, 0x0, 0x3, 0xeb, 0x20, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+626B "扫" */
    0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x0,
    0x6, 0xe0, 0x2, 0x33, 0x33, 0x37, 0xf0, 0xfe,
    0xef, 0xde, 0x20, 0x0, 0x0, 0x5e, 0x4, 0x48,
    0xf4, 0x40, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x6e,
    0x1, 0x0, 0x0, 0x0, 0x5e, 0x0, 0x6, 0xf8,
    0xe0, 0xcf, 0xff, 0xff, 0xe0, 0x2, 0xbf, 0xa2,
    0x2, 0x21, 0x11, 0x6e, 0xa, 0xfd, 0xe0, 0x0,
    0x0, 0x0, 0x5, 0xe0, 0x73, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0x6, 0xe0, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x6e, 0x0, 0x54, 0x44,
    0x44, 0x8e, 0x0, 0x6, 0xe0, 0xd, 0xdd, 0xdd,
    0xde, 0xf0, 0xb, 0xe8, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+62DF "拟" */
    0x0, 0xf, 0x40, 0x0, 0x17, 0x0, 0x46, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x2f, 0x40, 0x7c, 0x0,
    0x0, 0xf, 0x30, 0x3f, 0xa, 0xc0, 0x7c, 0x0,
    0x1f, 0xef, 0xeb, 0x3f, 0x3, 0xf4, 0x7c, 0x0,
    0x2, 0x2f, 0x42, 0x3e, 0x0, 0x72, 0x7c, 0x0,
    0x0, 0xf, 0x30, 0x3e, 0x0, 0x0, 0x7c, 0x0,
    0x0, 0xf, 0x79, 0x3e, 0x0, 0x0, 0x7b, 0x0,
    0x3, 0x9f, 0xc4, 0x3e, 0x0, 0x20, 0x8a, 0x0,
    0x2f, 0x9f, 0x30, 0x3e, 0x9, 0xb0, 0xb8, 0x0,
    0x1, 0xf, 0x30, 0x3e, 0x8e, 0x20, 0xe5, 0x0,
    0x0, 0xf, 0x30, 0x4f, 0xe2, 0x7, 0xfc, 0x0,
    0x0, 0xf, 0x30, 0x6e, 0x20, 0x5f, 0x6d, 0x80,
    0x0, 0x3f, 0x20, 0x0, 0x9, 0xf6, 0x4, 0xf3,
    0x4, 0xfc, 0x0, 0x3, 0xee, 0x40, 0x0, 0xa3,
    0x0, 0x10, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0,
    0x0, 0x1f, 0x10, 0x0, 0x3, 0xf2, 0x0, 0x0,
    0x0, 0x1f, 0x10, 0x58, 0x88, 0xfb, 0x88, 0x90,
    0x0, 0x1f, 0x10, 0x58, 0x88, 0x88, 0x98, 0x90,
    0x3f, 0xef, 0xe9, 0x1, 0xa0, 0x0, 0xf4, 0x0,
    0x3, 0x3f, 0x32, 0x0, 0xe7, 0x4, 0xf0, 0x0,
    0x0, 0x1f, 0x10, 0x22, 0x86, 0x2a, 0xb2, 0x30,
    0x0, 0x1f, 0x12, 0x9d, 0xdd, 0xdd, 0xdd, 0xd0,
    0x1, 0x5f, 0xe5, 0x0, 0xd, 0x50, 0x0, 0x0,
    0x3f, 0xcf, 0x11, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4, 0x1f, 0x10, 0x11, 0xe6, 0x3, 0xf1, 0x0,
    0x0, 0x1f, 0x10, 0x6, 0xf7, 0x1a, 0x80, 0x0,
    0x0, 0x1f, 0x10, 0x0, 0x4b, 0xff, 0x30, 0x0,
    0x1, 0x3f, 0x0, 0x1, 0x5c, 0xda, 0xfa, 0x20,
    0x9, 0xfc, 0x2, 0xdf, 0xd6, 0x0, 0x3c, 0xe1,
    0x2, 0x30, 0x0, 0x42, 0x0, 0x0, 0x0, 0x20,

    /* U+63CF "描" */
    0x0, 0x2f, 0x10, 0x0, 0xc4, 0x8, 0xa0, 0x0,
    0x0, 0x2f, 0x10, 0x0, 0xc4, 0x7, 0xa0, 0x0,
    0x0, 0x2f, 0x11, 0xa9, 0xeb, 0x9c, 0xd9, 0x94,
    0x13, 0x4f, 0x33, 0x88, 0xea, 0x8b, 0xd8, 0x83,
    0x4e, 0xdf, 0xd8, 0x0, 0xc4, 0x7, 0xa0, 0x0,
    0x0, 0x2f, 0x10, 0x0, 0x10, 0x0, 0x10, 0x0,
    0x0, 0x2f, 0x54, 0x6f, 0xff, 0xff, 0xff, 0x50,
    0x4, 0xaf, 0xc3, 0x4e, 0x1, 0xf1, 0xe, 0x40,
    0x2e, 0xaf, 0x10, 0x4e, 0x1, 0xf1, 0xe, 0x40,
    0x0, 0x2f, 0x10, 0x4f, 0xee, 0xfe, 0xef, 0x40,
    0x0, 0x2f, 0x10, 0x4e, 0x12, 0xf2, 0x1e, 0x40,
    0x0, 0x2f, 0x10, 0x4e, 0x1, 0xf1, 0xe, 0x40,
    0x0, 0x3f, 0x0, 0x4e, 0x23, 0xf3, 0x2e, 0x40,
    0x5, 0xde, 0x0, 0x4f, 0xcc, 0xcc, 0xcf, 0x40,
    0x4, 0xd4, 0x0, 0x6e, 0x0, 0x0, 0xe, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6536 "收" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe5, 0x0, 0x4f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0x0, 0x9d, 0x0, 0x0, 0x0,
    0xb, 0x30, 0xd4, 0x0, 0xf8, 0x22, 0x22, 0x20,
    0xf, 0x40, 0xd4, 0x7, 0xff, 0xff, 0xff, 0xf4,
    0xe, 0x40, 0xd4, 0xe, 0x70, 0x0, 0xf2, 0x0,
    0xe, 0x40, 0xd4, 0x8e, 0xd4, 0x2, 0xf0, 0x0,
    0xe, 0x40, 0xd5, 0xe5, 0xb8, 0x5, 0xd0, 0x0,
    0xe, 0x40, 0xd4, 0x0, 0x6d, 0x9, 0x90, 0x0,
    0xf, 0x65, 0xe4, 0x0, 0x1f, 0x4e, 0x40, 0x0,
    0x4f, 0xe9, 0xe4, 0x0, 0xa, 0xfd, 0x0, 0x0,
    0x4, 0x0, 0xd4, 0x0, 0x7, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0x0, 0x8f, 0xaf, 0x70, 0x0,
    0x0, 0x0, 0xd4, 0x4c, 0xf5, 0x4, 0xed, 0x61,
    0x0, 0x0, 0xd7, 0xdb, 0x20, 0x0, 0x19, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+673A "机" */
    0x0, 0xe, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x60, 0x2, 0xff, 0xff, 0x60, 0x0,
    0x0, 0xd, 0x60, 0x2, 0xf2, 0x1d, 0x60, 0x0,
    0x2d, 0xdf, 0xed, 0x92, 0xf0, 0xd, 0x60, 0x0,
    0x3, 0x4f, 0x72, 0x22, 0xf0, 0xd, 0x60, 0x0,
    0x0, 0x6f, 0x60, 0x2, 0xf0, 0xd, 0x60, 0x0,
    0x0, 0xcf, 0xba, 0x2, 0xf0, 0xd, 0x60, 0x0,
    0x2, 0xfe, 0x8f, 0x63, 0xf0, 0xd, 0x60, 0x0,
    0xa, 0xad, 0x67, 0x55, 0xd0, 0xd, 0x60, 0x0,
    0x3f, 0x4d, 0x60, 0x7, 0xb0, 0xd, 0x60, 0x0,
    0x3a, 0xd, 0x60, 0xb, 0x70, 0xd, 0x60, 0x0,
    0x0, 0xd, 0x60, 0x2f, 0x20, 0xd, 0x62, 0x70,
    0x0, 0xe, 0x60, 0xca, 0x0, 0xd, 0x85, 0xf0,
    0x0, 0xe, 0x64, 0xd1, 0x0, 0x9, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+675F "束" */
    0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x61, 0x21,
    0x11, 0x16, 0xe1, 0x11, 0x11, 0x21, 0x1, 0x44,
    0x44, 0x8f, 0x44, 0x44, 0x40, 0x0, 0x5f, 0xcc,
    0xcd, 0xfc, 0xcc, 0xdf, 0x0, 0x4, 0xe0, 0x0,
    0x5e, 0x0, 0x4, 0xe0, 0x0, 0x5f, 0xdd, 0xde,
    0xfd, 0xdd, 0xef, 0x0, 0x1, 0x33, 0x3c, 0xff,
    0xe8, 0x33, 0x30, 0x0, 0x0, 0x8, 0xe7, 0xf6,
    0xe2, 0x0, 0x0, 0x0, 0xa, 0xe3, 0x5f, 0xa,
    0xe2, 0x0, 0x0, 0x4e, 0xe2, 0x5, 0xf0, 0xa,
    0xf8, 0x10, 0x9f, 0xa0, 0x0, 0x5f, 0x0, 0x6,
    0xef, 0x41, 0x40, 0x0, 0x6, 0xf0, 0x0, 0x0,
    0x50,

    /* U+6A21 "模" */
    0x0, 0xf, 0x30, 0x0, 0x98, 0x9, 0x80, 0x0,
    0x0, 0xf, 0x30, 0xbb, 0xed, 0xbe, 0xdb, 0xa0,
    0x0, 0xf, 0x30, 0x22, 0x99, 0x2a, 0x92, 0x20,
    0x6, 0x6f, 0x86, 0x0, 0x33, 0x4, 0x30, 0x0,
    0xa, 0xaf, 0xba, 0x1f, 0xee, 0xee, 0xef, 0x30,
    0x0, 0x4f, 0x30, 0x1f, 0x0, 0x0, 0xd, 0x30,
    0x0, 0xaf, 0x92, 0x1f, 0xee, 0xee, 0xef, 0x30,
    0x2, 0xff, 0x9c, 0x1f, 0x0, 0x0, 0xd, 0x30,
    0xa, 0x9f, 0x3b, 0x3e, 0xee, 0xfe, 0xee, 0x30,
    0x4e, 0x1f, 0x30, 0x0, 0x4, 0xd0, 0x0, 0x0,
    0x1, 0xf, 0x30, 0xee, 0xef, 0xff, 0xee, 0xf0,
    0x0, 0xf, 0x30, 0x0, 0x1e, 0x8e, 0x50, 0x0,
    0x0, 0xf, 0x30, 0x6, 0xe7, 0x3, 0xe9, 0x10,
    0x0, 0xf, 0x34, 0xfb, 0x30, 0x0, 0x1b, 0xf2,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+7ED3 "结" */
    0x0, 0xa, 0x30, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0x0, 0x3f, 0x40, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0x0, 0xab, 0x0, 0x13, 0x33, 0xe7, 0x33, 0x41,
    0x2, 0xf2, 0x8, 0x6d, 0xcc, 0xfd, 0xcc, 0xd4,
    0xc, 0xa0, 0x6f, 0x30, 0x0, 0xd5, 0x0, 0x0,
    0x5f, 0xdd, 0xf5, 0x2, 0x22, 0xe6, 0x22, 0x10,
    0x16, 0x3b, 0x90, 0x9, 0xdd, 0xdd, 0xdd, 0x70,
    0x0, 0x4d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xe8, 0x7a, 0x24, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xfd, 0xa8, 0x4, 0xe0, 0x0, 0xe, 0x40,
    0x2, 0x0, 0x0, 0x4, 0xe0, 0x0, 0xe, 0x40,
    0x0, 0x3, 0x58, 0x34, 0xe0, 0x0, 0xe, 0x40,
    0x4f, 0xff, 0xda, 0x24, 0xff, 0xff, 0xff, 0x40,
    0x5, 0x20, 0x0, 0x4, 0xe0, 0x0, 0xe, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+7F6E "置" */
    0xd, 0xed, 0xde, 0xdd, 0xfd, 0xde, 0xc0, 0x0,
    0xc6, 0x4, 0xa0, 0xd, 0x20, 0x7c, 0x0, 0xa,
    0xcc, 0xcc, 0xde, 0xcc, 0xcc, 0xa0, 0x3, 0x66,
    0x66, 0x6c, 0xd6, 0x66, 0x66, 0x0, 0x48, 0x77,
    0x77, 0xea, 0x77, 0x77, 0x80, 0x0, 0x6, 0xbb,
    0xbf, 0xcb, 0xbb, 0x40, 0x0, 0x0, 0x89, 0x22,
    0x22, 0x22, 0xb5, 0x0, 0x0, 0x7, 0xed, 0xdd,
    0xdd, 0xdf, 0x50, 0x0, 0x0, 0x78, 0x11, 0x11,
    0x11, 0xb5, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x7b, 0x77, 0x77, 0x77,
    0xd5, 0x0, 0x0, 0x7, 0xb5, 0x55, 0x55, 0x5c,
    0x50, 0x0, 0xed, 0xee, 0xdd, 0xdd, 0xdd, 0xed,
    0xde, 0x0,

    /* U+8BBE "设" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdb, 0x0, 0xf, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x1e, 0x90, 0xf, 0x20, 0x1f, 0x30, 0x0,
    0x0, 0x3, 0x50, 0x2f, 0x10, 0x1f, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x0, 0x1f, 0x30, 0x0,
    0x4d, 0xdd, 0x15, 0xf4, 0x0, 0xf, 0xed, 0xd0,
    0x13, 0x5f, 0x14, 0x60, 0x0, 0x2, 0x54, 0x40,
    0x0, 0x2f, 0x14, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x2f, 0x10, 0x1f, 0x10, 0x7, 0xe0, 0x0,
    0x0, 0x2f, 0x10, 0xb, 0x80, 0xe, 0x70, 0x0,
    0x0, 0x2f, 0x39, 0x2, 0xf3, 0x9d, 0x0, 0x0,
    0x0, 0x2f, 0xec, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x4f, 0xb0, 0x1, 0xaf, 0xf8, 0x10, 0x0,
    0x0, 0x4d, 0x14, 0xaf, 0x91, 0x3b, 0xfc, 0x92,
    0x0, 0x0, 0x1b, 0x71, 0x0, 0x0, 0x26, 0x90,

    /* U+96C6 "集" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc5, 0x0, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xe1, 0x0, 0x5b, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xfe, 0xee, 0xee, 0xee, 0xee, 0x60,
    0x0, 0xcf, 0x20, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x9, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0xa, 0x3f, 0x20, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0xf, 0x31, 0x11, 0xf4, 0x11, 0x12, 0x0,
    0x0, 0x1f, 0xee, 0xee, 0xfe, 0xee, 0xee, 0x20,
    0x0, 0x4, 0x0, 0x7, 0xc0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xb, 0xd7, 0xc6, 0xe4, 0x0, 0x0,
    0x0, 0x16, 0xec, 0x16, 0xc0, 0x5f, 0xc6, 0x10,
    0x8, 0xff, 0x80, 0x6, 0xc0, 0x1, 0xaf, 0xf1,
    0x4, 0x82, 0x0, 0x7, 0xd0, 0x0, 0x1, 0x40
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 128, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 128, .box_w = 2, .box_h = 12, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 12, .adv_w = 128, .box_w = 8, .box_h = 2, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 20, .adv_w = 128, .box_w = 2, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22, .adv_w = 256, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 120, .adv_w = 256, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 225, .adv_w = 256, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 337, .adv_w = 256, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 457, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 570, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 690, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 818, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 946, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1074, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1194, .adv_w = 256, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1299, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1419, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1539, .adv_w = 256, .box_w = 15, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1637, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1757, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1, 0xd, 0xe, 0x4e0d, 0x4e6e, 0x5153, 0x51e6,
    0x624b, 0x62bf, 0x6385, 0x63af, 0x6516, 0x671a, 0x673f, 0x6a01,
    0x7eb3, 0x7f4e, 0x8b9e, 0x96a6
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 38567, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 20, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
 /*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

 /*Initialize a public general font descriptor*/
 #if LV_VERSION_CHECK(8, 0, 0)
 const lv_font_t lv_font_chinese_16 = {
 #else
 lv_font_t lv_font_chinese_16 = {
 #endif
     .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
     .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
     .line_height = 17,          /*The maximum line height required by the font*/
     .base_line = 3,             /*Baseline measured from the bottom of the line*/
 #if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
     .subpx = LV_FONT_SUBPX_NONE,
 #endif
 #if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
     .underline_position = -1,
     .underline_thickness = 1,
 #endif
     .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
 };



 #endif /*#if LV_FONT_CHINESE_16*/

