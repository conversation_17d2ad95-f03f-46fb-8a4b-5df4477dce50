﻿/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts:
 * :ABCDEFGHIJKLMNOPQRSTUVWXYZ
    abcdefghijklmnopqrstuvwxyz
    0123456789
    !@#$%^&*()_+{}|:"<>?[]\;',./`-=
    信号供电电压固件版本背包协议源蜂鸣器打开关闭背光屏幕背光开机动画制式系统语言输出保存并退出中文自动
    频谱扫描频道校准安装接收机天线并打开图传开始
    simhei.ttf
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_FONT_CHINESE_12
#define LV_FONT_CHINESE_12 1
#endif

#if LV_FONT_CHINESE_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xb8, 0xa7, 0xa7, 0x96, 0x85, 0x74, 0x42, 0x54,
    0xc9,

    /* U+0022 "\"" */
    0x78, 0x96, 0x66, 0x84, 0x33, 0x42,

    /* U+0023 "#" */
    0x2, 0x90, 0xc0, 0x3, 0x80, 0xc0, 0x5, 0x70,
    0xb0, 0xbf, 0xff, 0xf9, 0x8, 0x33, 0x80, 0xa,
    0x15, 0x60, 0xbf, 0xff, 0xf9, 0xc, 0x9, 0x20,
    0xb, 0xb, 0x0,

    /* U+0024 "$" */
    0x0, 0x63, 0x0, 0x7, 0xfe, 0x40, 0x1e, 0x77,
    0xd0, 0x2d, 0x63, 0x60, 0xc, 0xe3, 0x0, 0x0,
    0xbd, 0x20, 0x0, 0x6a, 0xe0, 0x5a, 0x64, 0xf1,
    0x2e, 0x76, 0xe0, 0x8, 0xfe, 0x50, 0x0, 0x63,
    0x0, 0x0, 0x31, 0x0,

    /* U+0025 "%" */
    0x4d, 0x30, 0x50, 0xa4, 0x90, 0x60, 0xb2, 0x96,
    0x0, 0xa4, 0x86, 0x0, 0x3c, 0x67, 0xc2, 0x0,
    0x6b, 0x47, 0x1, 0x5b, 0x29, 0x6, 0xb, 0x38,
    0x6, 0x5, 0xd3,

    /* U+0026 "&" */
    0x5, 0xec, 0x0, 0xc, 0x2b, 0x30, 0xc, 0x1c,
    0x10, 0x9, 0xd8, 0x0, 0xb, 0xd0, 0x0, 0x78,
    0xb6, 0x94, 0xa4, 0x1e, 0xe1, 0x89, 0x8, 0xf4,
    0x1b, 0xeb, 0x66,

    /* U+0027 "'" */
    0xe1, 0xd0, 0x70,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x65, 0x2, 0xb0, 0xa, 0x20,
    0xb, 0x0, 0x38, 0x0, 0x47, 0x0, 0x38, 0x0,
    0xb, 0x0, 0xa, 0x20, 0x2, 0xb0, 0x0, 0x55,

    /* U+0029 ")" */
    0x0, 0x0, 0x74, 0x0, 0xc, 0x10, 0x4, 0x80,
    0x0, 0xb0, 0x0, 0xa0, 0x0, 0x91, 0x0, 0xa0,
    0x0, 0xb0, 0x4, 0x70, 0x1c, 0x0, 0x73, 0x0,

    /* U+002A "*" */
    0x0, 0x77, 0x0, 0x75, 0x66, 0x57, 0x2b, 0xdd,
    0xb2, 0x7, 0xff, 0x70, 0x89, 0x77, 0x98, 0x0,
    0x76, 0x0, 0x0, 0x22, 0x0,

    /* U+002B "+" */
    0x0, 0x53, 0x0, 0x0, 0x75, 0x0, 0x0, 0x75,
    0x0, 0x9d, 0xed, 0xd8, 0x0, 0x75, 0x0, 0x0,
    0x75, 0x0,

    /* U+002C "," */
    0x1, 0x0, 0xf1, 0x8, 0x0, 0x30,

    /* U+002D "-" */
    0x9d, 0xdd, 0xd7,

    /* U+002E "." */
    0x1, 0x1, 0xf0,

    /* U+002F "/" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x54, 0x0, 0x0,
    0x90, 0x0, 0x6, 0x30, 0x0, 0x9, 0x0, 0x0,
    0x73, 0x0, 0x0, 0x90, 0x0, 0x7, 0x20, 0x0,
    0x18, 0x0, 0x0, 0x82, 0x0, 0x0, 0x20, 0x0,
    0x0,

    /* U+0030 "0" */
    0x5, 0xed, 0x30, 0x1e, 0x33, 0xd0, 0x6a, 0x0,
    0xb3, 0x86, 0x0, 0x95, 0x95, 0x0, 0x95, 0x86,
    0x0, 0x95, 0x5a, 0x0, 0xc2, 0xe, 0x45, 0xd0,
    0x3, 0xcc, 0x20, 0x0, 0x0, 0x0,

    /* U+0031 "1" */
    0x6, 0x94, 0xf9, 0xa7, 0x90, 0x69, 0x6, 0x90,
    0x69, 0x6, 0x90, 0x69, 0x6, 0x90,

    /* U+0032 "2" */
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x60, 0x4c, 0x22,
    0xf1, 0x32, 0x0, 0xd2, 0x0, 0x1, 0xe0, 0x0,
    0xa, 0x70, 0x0, 0x4d, 0x0, 0x1, 0xe2, 0x0,
    0xd, 0x60, 0x0, 0x5f, 0xee, 0xe4,

    /* U+0033 "3" */
    0x4, 0xbc, 0x40, 0x1d, 0x23, 0xe0, 0x24, 0x0,
    0xd2, 0x0, 0x5, 0xc0, 0x0, 0x7f, 0x60, 0x0,
    0x1, 0xe1, 0x23, 0x0, 0xb4, 0x4d, 0x12, 0xe1,
    0x7, 0xed, 0x40,

    /* U+0034 "4" */
    0x0, 0x6, 0xa0, 0x0, 0x1e, 0xa0, 0x0, 0xaa,
    0xa0, 0x4, 0xc4, 0xa0, 0xd, 0x24, 0xa0, 0x87,
    0x4, 0xa0, 0xcd, 0xde, 0xfa, 0x0, 0x4, 0xa0,
    0x0, 0x4, 0xa0,

    /* U+0035 "5" */
    0xc, 0xee, 0xe0, 0xd, 0x0, 0x0, 0x2b, 0x0,
    0x0, 0x5e, 0xee, 0x50, 0x56, 0x2, 0xe0, 0x0,
    0x0, 0xb4, 0x51, 0x0, 0xc3, 0x88, 0x5, 0xe0,
    0x1b, 0xfc, 0x30,

    /* U+0036 "6" */
    0x0, 0x1e, 0x10, 0x0, 0x97, 0x0, 0x3, 0xd0,
    0x0, 0xc, 0xdb, 0x50, 0x3f, 0x65, 0xe5, 0x79,
    0x0, 0x69, 0x77, 0x0, 0x59, 0x3d, 0x10, 0xc5,
    0x6, 0xde, 0x90,

    /* U+0037 "7" */
    0x7e, 0xee, 0xf8, 0x0, 0x0, 0xb3, 0x0, 0x3,
    0xb0, 0x0, 0xa, 0x40, 0x0, 0x1d, 0x0, 0x0,
    0x68, 0x0, 0x0, 0xc3, 0x0, 0x0, 0xe0, 0x0,
    0x4, 0xb0, 0x0,

    /* U+0038 "8" */
    0x8, 0xee, 0x50, 0x3a, 0x2, 0xe0, 0x58, 0x0,
    0xd1, 0x2d, 0x46, 0xc0, 0xc, 0xcd, 0x60, 0x78,
    0x0, 0xc3, 0xa5, 0x0, 0x95, 0x7b, 0x24, 0xe2,
    0x1a, 0xfe, 0x70,

    /* U+0039 "9" */
    0x9, 0xed, 0x40, 0x6b, 0x14, 0xe1, 0xb4, 0x0,
    0xc4, 0xb5, 0x0, 0xe2, 0x6d, 0x5a, 0xd0, 0x5,
    0x9f, 0x50, 0x0, 0x3d, 0x0, 0x0, 0xb5, 0x0,
    0x3, 0xc0, 0x0,

    /* U+003A ":" */
    0xa6, 0x10, 0x0, 0x0, 0x10, 0xa6,

    /* U+003B ";" */
    0xa6, 0x10, 0x0, 0x0, 0x10, 0xa6, 0x53, 0x30,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x9,
    0x50, 0x0, 0x95, 0x0, 0x9, 0x50, 0x0, 0x59,
    0x0, 0x0, 0x8, 0x60, 0x0, 0x0, 0x87, 0x0,
    0x0, 0x7, 0x70, 0x0, 0x0, 0x74, 0x0, 0x0,
    0x0,

    /* U+003D "=" */
    0x9d, 0xdd, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0xdd, 0xd7,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x58, 0x0, 0x0, 0x6, 0x80,
    0x0, 0x0, 0x68, 0x0, 0x0, 0x6, 0x80, 0x0,
    0x0, 0xa4, 0x0, 0x7, 0x70, 0x0, 0x86, 0x0,
    0x9, 0x60, 0x0, 0x65, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x6, 0xee, 0x50, 0x2e, 0x14, 0xf0, 0x49, 0x1,
    0xf1, 0x0, 0x7, 0xc0, 0x0, 0x3e, 0x20, 0x0,
    0x85, 0x0, 0x0, 0x20, 0x0, 0x0, 0x21, 0x0,
    0x0, 0xb5, 0x0,

    /* U+0040 "@" */
    0x3, 0x66, 0x50, 0x16, 0x0, 0x42, 0x70, 0x58,
    0x57, 0x73, 0x86, 0x47, 0x78, 0x29, 0x17, 0x7a,
    0x1b, 0x16, 0x74, 0xb8, 0x90, 0x34, 0x0, 0x20,
    0x5, 0x66, 0x40,

    /* U+0041 "A" */
    0x0, 0xa8, 0x0, 0x0, 0xec, 0x0, 0x2, 0xcd,
    0x10, 0x6, 0x8a, 0x50, 0xb, 0x46, 0x90, 0xe,
    0xff, 0xd0, 0x3c, 0x0, 0xe1, 0x79, 0x0, 0xa5,
    0xb5, 0x0, 0x7a,

    /* U+0042 "B" */
    0x7f, 0xfe, 0x60, 0x79, 0x2, 0xe1, 0x79, 0x0,
    0xd4, 0x79, 0x2, 0xe1, 0x7f, 0xff, 0x80, 0x79,
    0x2, 0xd4, 0x79, 0x0, 0xa7, 0x79, 0x1, 0xe5,
    0x7f, 0xfe, 0x90,

    /* U+0043 "C" */
    0x3, 0xde, 0x70, 0xe, 0x41, 0xd3, 0x5c, 0x0,
    0x97, 0x89, 0x0, 0x11, 0x98, 0x0, 0x0, 0x89,
    0x0, 0x45, 0x6b, 0x0, 0x97, 0x1e, 0x31, 0xe3,
    0x4, 0xde, 0x60,

    /* U+0044 "D" */
    0x7f, 0xfb, 0x20, 0x79, 0x8, 0xe0, 0x79, 0x0,
    0xe4, 0x79, 0x0, 0xb6, 0x79, 0x0, 0xa7, 0x79,
    0x0, 0xb6, 0x79, 0x0, 0xd4, 0x79, 0x7, 0xd0,
    0x7f, 0xfb, 0x20,

    /* U+0045 "E" */
    0x5f, 0xff, 0xf3, 0x5b, 0x0, 0x0, 0x5b, 0x0,
    0x0, 0x5b, 0x0, 0x0, 0x5f, 0xff, 0xe0, 0x5b,
    0x0, 0x0, 0x5b, 0x0, 0x0, 0x5b, 0x0, 0x0,
    0x5f, 0xff, 0xf6,

    /* U+0046 "F" */
    0x7f, 0xff, 0xf7, 0x7a, 0x0, 0x0, 0x7a, 0x0,
    0x0, 0x7a, 0x0, 0x0, 0x7f, 0xff, 0x80, 0x7a,
    0x0, 0x0, 0x7a, 0x0, 0x0, 0x7a, 0x0, 0x0,
    0x7a, 0x0, 0x0,

    /* U+0047 "G" */
    0x4, 0xee, 0x40, 0x1e, 0x32, 0xe0, 0x5b, 0x0,
    0xc3, 0x89, 0x0, 0x52, 0x98, 0x0, 0x0, 0x89,
    0xf, 0xf5, 0x6b, 0x0, 0x95, 0x1f, 0x31, 0xd5,
    0x4, 0xed, 0xb5,

    /* U+0048 "H" */
    0x79, 0x0, 0xb5, 0x79, 0x0, 0xb5, 0x79, 0x0,
    0xb5, 0x79, 0x0, 0xb5, 0x7f, 0xff, 0xf5, 0x79,
    0x0, 0xb5, 0x79, 0x0, 0xb5, 0x79, 0x0, 0xb5,
    0x79, 0x0, 0xb5,

    /* U+0049 "I" */
    0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97,
    0x97,

    /* U+004A "J" */
    0x0, 0x0, 0xd4, 0x0, 0x0, 0xd4, 0x0, 0x0,
    0xd4, 0x0, 0x0, 0xd4, 0x0, 0x0, 0xd4, 0x35,
    0x0, 0xd3, 0x5b, 0x0, 0xd2, 0x2e, 0x24, 0xe0,
    0x7, 0xee, 0x50,

    /* U+004B "K" */
    0x79, 0x2, 0xe3, 0x79, 0xc, 0x70, 0x79, 0x7c,
    0x0, 0x7c, 0xf3, 0x0, 0x7f, 0xd8, 0x0, 0x7b,
    0x3f, 0x10, 0x79, 0xb, 0x70, 0x79, 0x3, 0xe0,
    0x79, 0x0, 0xb7,

    /* U+004C "L" */
    0x7a, 0x0, 0x0, 0x7a, 0x0, 0x0, 0x7a, 0x0,
    0x0, 0x7a, 0x0, 0x0, 0x7a, 0x0, 0x0, 0x7a,
    0x0, 0x0, 0x7a, 0x0, 0x0, 0x7a, 0x0, 0x0,
    0x7f, 0xff, 0xf6,

    /* U+004D "M" */
    0x9f, 0x0, 0xf7, 0x9f, 0x23, 0xf7, 0x9e, 0x46,
    0xe7, 0x9b, 0x79, 0xb7, 0x98, 0xab, 0x87, 0x96,
    0xcd, 0x77, 0x95, 0xdc, 0x77, 0x95, 0xa9, 0x77,
    0x95, 0x86, 0x77,

    /* U+004E "N" */
    0x8d, 0x0, 0xb5, 0x8f, 0x30, 0xb5, 0x8e, 0xa0,
    0xb5, 0x89, 0xe0, 0xb5, 0x88, 0x96, 0xb5, 0x88,
    0x3c, 0xb5, 0x88, 0xd, 0xe5, 0x88, 0x6, 0xf5,
    0x88, 0x1, 0xf5,

    /* U+004F "O" */
    0x7, 0xee, 0x50, 0x3e, 0x23, 0xf1, 0x79, 0x0,
    0xb5, 0xa7, 0x0, 0xa7, 0xa7, 0x0, 0x98, 0x97,
    0x0, 0xa7, 0x79, 0x0, 0xb5, 0x3e, 0x23, 0xf1,
    0x6, 0xee, 0x50,

    /* U+0050 "P" */
    0x6f, 0xfe, 0x70, 0x6b, 0x1, 0xe3, 0x6b, 0x0,
    0x97, 0x6b, 0x0, 0x97, 0x6b, 0x2, 0xe3, 0x6f,
    0xfe, 0x70, 0x6b, 0x0, 0x0, 0x6b, 0x0, 0x0,
    0x6b, 0x0, 0x0,

    /* U+0051 "Q" */
    0x7, 0xee, 0x50, 0x3e, 0x23, 0xf1, 0x79, 0x0,
    0xb5, 0xa7, 0x0, 0xa7, 0xa7, 0x0, 0x98, 0x97,
    0x4, 0xa7, 0x79, 0xe, 0xd5, 0x3e, 0x28, 0xf1,
    0x6, 0xee, 0xf1, 0x0, 0x0, 0x30,

    /* U+0052 "R" */
    0x7f, 0xfe, 0x70, 0x79, 0x2, 0xe3, 0x79, 0x0,
    0xb5, 0x79, 0x2, 0xe3, 0x7f, 0xff, 0x70, 0x79,
    0xe, 0x20, 0x79, 0x8, 0x80, 0x79, 0x2, 0xe0,
    0x79, 0x0, 0xb6,

    /* U+0053 "S" */
    0x7, 0xee, 0x50, 0x3e, 0x13, 0xe0, 0x5c, 0x0,
    0x71, 0x1e, 0x81, 0x0, 0x2, 0xbf, 0x50, 0x0,
    0x4, 0xf3, 0x77, 0x0, 0xb6, 0x5d, 0x11, 0xe3,
    0x9, 0xee, 0x70,

    /* U+0054 "T" */
    0x7f, 0xff, 0xf5, 0x0, 0x97, 0x0, 0x0, 0x97,
    0x0, 0x0, 0x97, 0x0, 0x0, 0x97, 0x0, 0x0,
    0x97, 0x0, 0x0, 0x97, 0x0, 0x0, 0x97, 0x0,
    0x0, 0x97, 0x0,

    /* U+0055 "U" */
    0x79, 0x0, 0xb6, 0x79, 0x0, 0xb6, 0x79, 0x0,
    0xb6, 0x79, 0x0, 0xb6, 0x79, 0x0, 0xb6, 0x79,
    0x0, 0xb6, 0x79, 0x0, 0xb5, 0x4e, 0x12, 0xe2,
    0x8, 0xee, 0x70,

    /* U+0056 "V" */
    0xc5, 0x0, 0x89, 0x89, 0x0, 0xc5, 0x4c, 0x0,
    0xf1, 0xf, 0x3, 0xc0, 0xb, 0x47, 0x80, 0x7,
    0x8b, 0x40, 0x3, 0xce, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0xb8, 0x0,

    /* U+0057 "W" */
    0xe1, 0xa7, 0x5b, 0xc3, 0xc9, 0x69, 0xa4, 0xcb,
    0x77, 0x86, 0xbb, 0x95, 0x7a, 0x9b, 0xa3, 0x5d,
    0x7a, 0xd2, 0x3f, 0x58, 0xf0, 0x1f, 0x36, 0xe0,
    0xf, 0x14, 0xc0,

    /* U+0058 "X" */
    0x5c, 0x0, 0xe2, 0xd, 0x25, 0xb0, 0x6, 0x9c,
    0x30, 0x0, 0xec, 0x0, 0x0, 0xc9, 0x0, 0x2,
    0xde, 0x0, 0xa, 0x69, 0x70, 0x2e, 0x2, 0xe0,
    0x98, 0x0, 0xb6,

    /* U+0059 "Y" */
    0x97, 0x0, 0x97, 0x2e, 0x1, 0xe0, 0xb, 0x57,
    0x80, 0x4, 0xcd, 0x20, 0x0, 0xca, 0x0, 0x0,
    0x97, 0x0, 0x0, 0x97, 0x0, 0x0, 0x97, 0x0,
    0x0, 0x97, 0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xf4, 0x0, 0x1, 0xe1, 0x0, 0x9,
    0x70, 0x0, 0x2e, 0x0, 0x0, 0xa6, 0x0, 0x3,
    0xd0, 0x0, 0xc, 0x50, 0x0, 0x4c, 0x0, 0x0,
    0x9f, 0xff, 0xf6,

    /* U+005B "[" */
    0x9d, 0xd3, 0xa2, 0x0, 0xa2, 0x0, 0xa2, 0x0,
    0xa2, 0x0, 0xa2, 0x0, 0xa2, 0x0, 0xa2, 0x0,
    0xa2, 0x0, 0xa2, 0x0, 0x9d, 0xd3,

    /* U+005C "\\" */
    0x23, 0x0, 0x0, 0xb, 0x0, 0x0, 0xa, 0x10,
    0x0, 0x4, 0x70, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x83, 0x0, 0x0, 0x29, 0x0, 0x0, 0xb, 0x0,
    0x0, 0x6, 0x50, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xa1,

    /* U+005D "]" */
    0x5d, 0xd7, 0x0, 0x48, 0x0, 0x48, 0x0, 0x48,
    0x0, 0x48, 0x0, 0x48, 0x0, 0x48, 0x0, 0x48,
    0x0, 0x48, 0x0, 0x48, 0x5d, 0xd7,

    /* U+005E "^" */
    0x2, 0xed, 0x0, 0x1b, 0x24, 0xa0,

    /* U+005F "_" */
    0x99, 0x99, 0x98,

    /* U+0060 "`" */
    0x27, 0x0, 0xb, 0x70, 0x1, 0xc1,

    /* U+0061 "a" */
    0x7, 0xee, 0x60, 0x1c, 0x1, 0xe0, 0x2, 0x7b,
    0xf0, 0x3e, 0x62, 0xe0, 0x7a, 0x4, 0xf0, 0x1c,
    0xda, 0xe2,

    /* U+0062 "b" */
    0x78, 0x0, 0x0, 0x78, 0x0, 0x0, 0x78, 0x0,
    0x0, 0x7a, 0xde, 0x50, 0x7e, 0x12, 0xe0, 0x78,
    0x0, 0xc3, 0x78, 0x0, 0xc3, 0x7d, 0x12, 0xe1,
    0x7b, 0xde, 0x50,

    /* U+0063 "c" */
    0x6, 0xee, 0x60, 0x3d, 0x11, 0xe1, 0x88, 0x0,
    0x0, 0x88, 0x0, 0x42, 0x3d, 0x11, 0xe2, 0x6,
    0xee, 0x60,

    /* U+0064 "d" */
    0x0, 0x0, 0xc2, 0x0, 0x0, 0xc2, 0x0, 0x0,
    0xc2, 0x8, 0xeb, 0xd2, 0x4d, 0x4, 0xf2, 0x88,
    0x0, 0xd2, 0x87, 0x0, 0xd2, 0x4c, 0x2, 0xf2,
    0x9, 0xdb, 0xd2,

    /* U+0065 "e" */
    0x6, 0xdd, 0x60, 0x3d, 0x1, 0xe1, 0x7e, 0xcc,
    0xd3, 0x79, 0x0, 0x10, 0x3d, 0x11, 0xe2, 0x6,
    0xee, 0x60,

    /* U+0066 "f" */
    0x0, 0x5e, 0xe4, 0x0, 0xd2, 0x0, 0x0, 0xe0,
    0x0, 0x6d, 0xfd, 0xc0, 0x0, 0xe0, 0x0, 0x0,
    0xe0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0xe0, 0x0,
    0x0, 0xe0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x0, 0x6, 0xdc, 0xa8, 0xe, 0x7,
    0x80, 0xe, 0x7, 0x80, 0xb, 0xdb, 0x10, 0x3a,
    0x0, 0x0, 0xe, 0xed, 0xa0, 0x67, 0x0, 0xa5,
    0x2b, 0xbb, 0xa0,

    /* U+0068 "h" */
    0x59, 0x0, 0x0, 0x59, 0x0, 0x0, 0x59, 0x0,
    0x0, 0x59, 0xad, 0xa0, 0x5f, 0x30, 0xd3, 0x5b,
    0x0, 0xb4, 0x59, 0x0, 0xb4, 0x59, 0x0, 0xb4,
    0x59, 0x0, 0xb4,

    /* U+0069 "i" */
    0x96, 0x21, 0x0, 0x96, 0x96, 0x96, 0x96, 0x96,
    0x96,

    /* U+006A "j" */
    0x0, 0xa5, 0x0, 0x31, 0x0, 0x0, 0x0, 0xa5,
    0x0, 0xa5, 0x0, 0xa5, 0x0, 0xa5, 0x0, 0xa5,
    0x0, 0xa4, 0x10, 0xc3, 0xde, 0xa0,

    /* U+006B "k" */
    0x4a, 0x0, 0x0, 0x4a, 0x0, 0x0, 0x4a, 0x0,
    0x0, 0x4a, 0x7, 0xb0, 0x4a, 0x5c, 0x0, 0x4d,
    0xe9, 0x0, 0x4e, 0x2d, 0x20, 0x4a, 0x5, 0xa0,
    0x4a, 0x0, 0xd3,

    /* U+006C "l" */
    0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86,
    0x86,

    /* U+006D "m" */
    0xda, 0xe8, 0xe7, 0xd5, 0x8a, 0x4b, 0xd2, 0x86,
    0x3b, 0xd2, 0x86, 0x3b, 0xd2, 0x86, 0x3b, 0xd2,
    0x86, 0x3b,

    /* U+006E "n" */
    0x5a, 0xac, 0xa0, 0x5f, 0x20, 0xd3, 0x5b, 0x0,
    0xb4, 0x59, 0x0, 0xb4, 0x59, 0x0, 0xb4, 0x59,
    0x0, 0xb4,

    /* U+006F "o" */
    0x6, 0xed, 0x50, 0x3d, 0x12, 0xe2, 0x88, 0x0,
    0x96, 0x88, 0x0, 0x96, 0x3d, 0x11, 0xe2, 0x6,
    0xed, 0x50,

    /* U+0070 "p" */
    0x7b, 0xcd, 0x50, 0x7d, 0x1, 0xe1, 0x78, 0x0,
    0xc3, 0x79, 0x0, 0xc3, 0x7e, 0x13, 0xe0, 0x7a,
    0xde, 0x50, 0x78, 0x0, 0x0, 0x78, 0x0, 0x0,

    /* U+0071 "q" */
    0x8, 0xeb, 0xd2, 0x4c, 0x3, 0xf2, 0x88, 0x0,
    0xd2, 0x88, 0x0, 0xd2, 0x4d, 0x4, 0xf2, 0x8,
    0xeb, 0xd2, 0x0, 0x0, 0xc2, 0x0, 0x0, 0xc2,

    /* U+0072 "r" */
    0xc4, 0xba, 0xcd, 0x40, 0xc6, 0x0, 0xc2, 0x0,
    0xc2, 0x0, 0xc2, 0x0,

    /* U+0073 "s" */
    0x7, 0xde, 0x60, 0xf, 0x0, 0xa0, 0xd, 0xa5,
    0x0, 0x0, 0x59, 0xd0, 0x2b, 0x0, 0xe1, 0x8,
    0xee, 0x80,

    /* U+0074 "t" */
    0x0, 0x60, 0x0, 0x1, 0xd0, 0x0, 0x8d, 0xfd,
    0xa0, 0x1, 0xd0, 0x0, 0x1, 0xd0, 0x0, 0x1,
    0xd0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x7e, 0xe1,

    /* U+0075 "u" */
    0x59, 0x0, 0xb4, 0x59, 0x0, 0xb4, 0x59, 0x0,
    0xb4, 0x59, 0x0, 0xc4, 0x4b, 0x3, 0xf4, 0xb,
    0xb9, 0xb4,

    /* U+0076 "v" */
    0x68, 0x0, 0xc3, 0x1d, 0x1, 0xe0, 0xc, 0x36,
    0x90, 0x6, 0x8b, 0x30, 0x1, 0xdd, 0x0, 0x0,
    0xc9, 0x0,

    /* U+0077 "w" */
    0xd1, 0xb8, 0x4a, 0xa4, 0xba, 0x77, 0x77, 0xab,
    0x94, 0x4b, 0x8a, 0xc1, 0x1f, 0x58, 0xe0, 0xe,
    0x36, 0xb0,

    /* U+0078 "x" */
    0x2e, 0x2, 0xe0, 0x8, 0x7b, 0x50, 0x0, 0xdb,
    0x0, 0x1, 0xec, 0x0, 0xa, 0x6a, 0x70, 0x4d,
    0x1, 0xe1,

    /* U+0079 "y" */
    0x78, 0x0, 0xc4, 0x2d, 0x1, 0xe0, 0xc, 0x35,
    0x90, 0x7, 0x8a, 0x40, 0x2, 0xdd, 0x0, 0x0,
    0xd9, 0x0, 0x0, 0xa4, 0x0, 0xf, 0xb0, 0x0,

    /* U+007A "z" */
    0x2d, 0xdd, 0xf0, 0x0, 0xa, 0x70, 0x0, 0x6b,
    0x0, 0x2, 0xd1, 0x0, 0xc, 0x40, 0x0, 0x5f,
    0xdd, 0xd2,

    /* U+007B "{" */
    0x4, 0xa1, 0xb, 0x30, 0xb, 0x30, 0xb, 0x30,
    0xb, 0x30, 0xc, 0x20, 0x4e, 0x0, 0xc, 0x20,
    0xb, 0x30, 0xb, 0x30, 0xb, 0x30, 0x5, 0xb1,

    /* U+007C "|" */
    0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86,
    0x86, 0x86, 0x86, 0x86, 0x86,

    /* U+007D "}" */
    0x2b, 0x20, 0x6, 0x80, 0x5, 0x90, 0x5, 0x90,
    0x5, 0x90, 0x4, 0x90, 0x1, 0xf2, 0x5, 0x90,
    0x5, 0x90, 0x5, 0x90, 0x6, 0x90, 0x2b, 0x30,

    /* U+4E2D "中" */
    0x0, 0x0, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0xe2, 0x0, 0x0, 0xcc,
    0xbb, 0xfb, 0xbb, 0xf0, 0xc3, 0x0, 0xe1, 0x0,
    0xf0, 0xc3, 0x0, 0xe1, 0x0, 0xf0, 0xcc, 0xcc,
    0xfc, 0xcc, 0xf0, 0xb3, 0x0, 0xe1, 0x0, 0xe0,
    0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0xf1, 0x0, 0x0,

    /* U+4EF6 "件" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0x0, 0xe, 0x0, 0x0, 0x0, 0xa6, 0x3a, 0xe,
    0x0, 0x0, 0x1, 0xf0, 0x68, 0xe, 0x0, 0x0,
    0xa, 0xf0, 0xcc, 0xcf, 0xcc, 0x70, 0x5c, 0xe4,
    0xc0, 0xe, 0x0, 0x0, 0x21, 0xe0, 0x20, 0xe,
    0x0, 0x0, 0x0, 0xe6, 0xdc, 0xcf, 0xcc, 0xd3,
    0x0, 0xe0, 0x0, 0xe, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0xe0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0xe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4F20 "传" */
    0x0, 0x16, 0x0, 0x64, 0x0, 0x0, 0x0, 0x7a,
    0x0, 0xa3, 0x0, 0x0, 0x0, 0xe3, 0xbc, 0xfc,
    0xcc, 0x70, 0x7, 0xf0, 0x0, 0xd0, 0x0, 0x0,
    0x2e, 0xe6, 0xcc, 0xfc, 0xcc, 0xc1, 0x75, 0xe0,
    0x6, 0x70, 0x0, 0x0, 0x0, 0xe0, 0xb, 0xdc,
    0xcc, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x79, 0x0,
    0x0, 0xe0, 0x2, 0x92, 0xd2, 0x0, 0x0, 0xe0,
    0x0, 0x7e, 0xd1, 0x0, 0x0, 0xf0, 0x0, 0x1,
    0xad, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+4F9B "供" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x2, 0xb0, 0xd1, 0x0, 0x0, 0xb4, 0x2, 0xb0,
    0xd1, 0x0, 0x3, 0xf0, 0x9d, 0xec, 0xfd, 0xb0,
    0xc, 0xf0, 0x2, 0xb0, 0xd1, 0x0, 0x48, 0xe0,
    0x2, 0xb0, 0xd1, 0x0, 0x0, 0xe0, 0x24, 0xb2,
    0xe3, 0x20, 0x0, 0xe1, 0xcb, 0xbb, 0xbb, 0xc2,
    0x0, 0xe0, 0x5, 0x70, 0x67, 0x0, 0x0, 0xe0,
    0x4e, 0x20, 0x1d, 0x60, 0x0, 0xe3, 0xd3, 0x0,
    0x2, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4FDD "保" */
    0x0, 0x14, 0x0, 0x0, 0x0, 0x0, 0x0, 0x59,
    0xf, 0xcc, 0xcf, 0x0, 0x0, 0xc1, 0xd, 0x0,
    0xd, 0x0, 0x4, 0xf0, 0xf, 0xbb, 0xbf, 0x0,
    0xb, 0xe0, 0x1, 0x1e, 0x11, 0x0, 0x67, 0xd0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0xd3, 0xdc, 0xff,
    0xec, 0xd3, 0x0, 0xd0, 0x5, 0x9e, 0xb1, 0x0,
    0x0, 0xd0, 0x3d, 0xe, 0x3c, 0x20, 0x0, 0xd4,
    0xd2, 0xe, 0x4, 0xe4, 0x0, 0xd0, 0x10, 0xe,
    0x0, 0x0,

    /* U+4FE1 "信" */
    0x0, 0x25, 0x0, 0x6, 0x0, 0x0, 0x0, 0x98,
    0x0, 0xa, 0x10, 0x0, 0x0, 0xe1, 0xcc, 0xcc,
    0xcc, 0xc6, 0x7, 0xf0, 0x15, 0x55, 0x55, 0x40,
    0x1d, 0xe0, 0x16, 0x55, 0x55, 0x50, 0x56, 0xd0,
    0x2c, 0xbb, 0xbb, 0x90, 0x0, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd0, 0x1e, 0xaa, 0xab, 0xa0,
    0x0, 0xd0, 0x1c, 0x0, 0x2, 0xa0, 0x0, 0xd0,
    0x1f, 0xcc, 0xcc, 0xa0, 0x0, 0xe0, 0x1c, 0x0,
    0x2, 0xb0,

    /* U+5149 "光" */
    0x0, 0x0, 0x7, 0xc0, 0x0, 0x0, 0x0, 0x40,
    0x7, 0xb0, 0x8, 0x20, 0x0, 0xc8, 0x7, 0xb0,
    0x4e, 0x10, 0x0, 0x3f, 0x17, 0xb0, 0xd5, 0x0,
    0x0, 0x3, 0x7, 0xb0, 0x20, 0x0, 0x3c, 0xcc,
    0xfd, 0xdf, 0xcc, 0xc7, 0x0, 0x0, 0xc2, 0x2d,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0x2d, 0x0, 0x0,
    0x0, 0x8, 0x90, 0x2d, 0x0, 0x20, 0x2, 0xad,
    0x10, 0x1e, 0x0, 0xb7, 0x4f, 0x90, 0x0, 0xa,
    0xcc, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5173 "关" */
    0x0, 0x3, 0x0, 0x3, 0x0, 0x0, 0x0, 0xb,
    0x70, 0xe, 0x40, 0x0, 0x0, 0x2, 0xa0, 0x4a,
    0x0, 0x0, 0x7, 0xdc, 0xcf, 0xdc, 0xcd, 0x20,
    0x0, 0x0, 0xb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x20, 0x0, 0x0, 0x1c, 0xcc, 0xcf, 0xec,
    0xcc, 0xa0, 0x0, 0x0, 0x3e, 0xd0, 0x0, 0x0,
    0x0, 0x1, 0xd4, 0x7c, 0x10, 0x0, 0x0, 0x6d,
    0x50, 0x7, 0xf9, 0x40, 0xd, 0xa1, 0x0, 0x0,
    0x3b, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+51C6 "准" */
    0x0, 0x0, 0x7, 0x44, 0x80, 0x0, 0xc, 0x20,
    0xe, 0x20, 0xb2, 0x0, 0x4, 0xe2, 0x5f, 0xcc,
    0xcc, 0xc5, 0x0, 0x62, 0xcc, 0x1, 0xd0, 0x0,
    0x0, 0x6, 0xec, 0x23, 0xe2, 0x20, 0x0, 0x4,
    0x6e, 0xaa, 0xfa, 0xa0, 0x0, 0xb2, 0x3c, 0x1,
    0xd0, 0x0, 0x2, 0xd0, 0x3e, 0xbb, 0xfb, 0xb0,
    0xb, 0x60, 0x3c, 0x1, 0xd0, 0x0, 0x1c, 0x0,
    0x3f, 0xdd, 0xfd, 0xea, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x0,

    /* U+51FA "出" */
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x46, 0x0, 0xe0,
    0x0, 0xa0, 0x67, 0x0, 0xe0, 0x0, 0xe0, 0x67,
    0x0, 0xe0, 0x0, 0xe0, 0x6d, 0xcc, 0xfc, 0xcc,
    0xe0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0xc0, 0x0,
    0xe0, 0x0, 0x84, 0xe0, 0x0, 0xe0, 0x0, 0x95,
    0xe0, 0x0, 0xe0, 0x0, 0x95, 0xea, 0xaa, 0xfa,
    0xaa, 0xd5, 0x22, 0x22, 0x22, 0x22, 0xa5,

    /* U+5236 "制" */
    0x0, 0xd, 0x20, 0x0, 0x0, 0xe0, 0xc, 0x3d,
    0x20, 0x0, 0x0, 0xf0, 0x1f, 0xbf, 0xcb, 0xa2,
    0xc0, 0xf0, 0x57, 0xd, 0x20, 0x1, 0xc0, 0xf0,
    0x59, 0x9f, 0xa9, 0xa2, 0xc0, 0xf0, 0x12, 0x2d,
    0x42, 0x21, 0xc0, 0xf0, 0x9, 0x9e, 0xa9, 0x91,
    0xc0, 0xf0, 0xe, 0x2d, 0x42, 0xf1, 0xc0, 0xf0,
    0xe, 0xd, 0x20, 0xe1, 0x70, 0xf0, 0xe, 0xd,
    0x2d, 0xa0, 0x0, 0xf0, 0x0, 0xd, 0x20, 0x0,
    0x1f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+52A8 "动" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x1, 0x11,
    0x11, 0x3, 0xb0, 0x0, 0x9, 0xbb, 0xb6, 0x3,
    0xb0, 0x0, 0x0, 0x0, 0x2, 0xab, 0xea, 0xd2,
    0x6c, 0xcc, 0xcc, 0x3, 0xa0, 0xc1, 0x0, 0xa5,
    0x0, 0x4, 0x90, 0xd0, 0x0, 0xd0, 0x92, 0x7,
    0x60, 0xe0, 0x7, 0x70, 0x6a, 0xa, 0x20, 0xe0,
    0x1f, 0xdc, 0xbe, 0x4c, 0x0, 0xe0, 0x3, 0x0,
    0x2, 0xc4, 0x3, 0xd0, 0x0, 0x0, 0x9, 0x70,
    0x6c, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5305 "包" */
    0x0, 0x7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xbb, 0xbb,
    0xbf, 0x0, 0x9, 0xa0, 0x0, 0x0, 0xf, 0x0,
    0x8b, 0xed, 0xcc, 0xf1, 0xf, 0x0, 0x0, 0xd1,
    0x0, 0xd1, 0xe, 0x0, 0x0, 0xdb, 0xbb, 0xf1,
    0xe, 0x0, 0x0, 0xd2, 0x11, 0x11, 0x5d, 0x0,
    0x0, 0xd1, 0x0, 0xb, 0xc5, 0x30, 0x0, 0xd3,
    0x0, 0x0, 0x0, 0xe4, 0x0, 0x6d, 0xcc, 0xcc,
    0xcd, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+534F "协" */
    0x0, 0xa0, 0x0, 0x94, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0xa4, 0x0, 0x0, 0x0, 0xe0, 0x36, 0xc9,
    0x66, 0x10, 0x6b, 0xfb, 0x86, 0xd7, 0x5e, 0x10,
    0x0, 0xe0, 0x0, 0xd2, 0xe, 0x10, 0x0, 0xe0,
    0xa3, 0xe0, 0xe, 0xa4, 0x0, 0xe0, 0xc3, 0xc0,
    0xd, 0x5a, 0x0, 0xe0, 0x48, 0x80, 0x1d, 0x2,
    0x0, 0xe0, 0xe, 0x10, 0x2c, 0x0, 0x0, 0xe0,
    0xa8, 0x6, 0xa9, 0x0, 0x0, 0xe1, 0x90, 0x5,
    0x60, 0x0,

    /* U+538B "压" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xdd,
    0xdd, 0xdd, 0xdd, 0x0, 0xe0, 0x0, 0x91, 0x0,
    0x0, 0xe, 0x0, 0xd, 0x10, 0x0, 0x0, 0xe0,
    0x0, 0xd1, 0x0, 0x0, 0xe, 0x8f, 0xff, 0xff,
    0xf3, 0x0, 0xd0, 0x0, 0xd1, 0x20, 0x0, 0x2c,
    0x0, 0xd, 0x1d, 0x30, 0x5, 0x90, 0x0, 0xd1,
    0x58, 0x0, 0xb6, 0x22, 0x2d, 0x32, 0x22, 0x3e,
    0x4c, 0xcc, 0xcc, 0xcc, 0xb0, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+53F7 "号" */
    0x0, 0x2f, 0xcc, 0xcc, 0xd9, 0x0, 0x0, 0x1c,
    0x0, 0x0, 0x49, 0x0, 0x0, 0x2f, 0xcc, 0xcc,
    0xda, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xcc, 0xec, 0xcc, 0xcc, 0xc6, 0x0, 0x5,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xa, 0xec, 0xcc,
    0xca, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x98, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5668 "器" */
    0x5, 0xda, 0xd4, 0xab, 0xad, 0x30, 0x5, 0xda,
    0xd4, 0xab, 0xad, 0x30, 0x0, 0x0, 0x9, 0x31,
    0x80, 0x0, 0xb, 0xbb, 0xbf, 0xcb, 0xdb, 0xc0,
    0x0, 0x2, 0xb7, 0x99, 0x30, 0x0, 0x3c, 0xda,
    0x30, 0x5, 0xbf, 0xe2, 0x6, 0xbb, 0xb3, 0x6b,
    0xbb, 0x40, 0x5, 0x70, 0x84, 0x92, 0x9, 0x30,
    0x5, 0xda, 0xd4, 0x9b, 0xad, 0x30, 0x5, 0x70,
    0x84, 0x92, 0x9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+56FA "固" */
    0xed, 0xdd, 0xdd, 0xdd, 0xde, 0xe0, 0x0, 0x28,
    0x0, 0xe, 0xe0, 0xbb, 0xce, 0xbb, 0x3e, 0xe0,
    0x11, 0x3c, 0x11, 0xe, 0xe0, 0x2, 0x4c, 0x21,
    0xe, 0xe0, 0x3e, 0xaa, 0xbc, 0xe, 0xe0, 0x2a,
    0x0, 0x2c, 0xe, 0xe0, 0x2c, 0xcc, 0xc9, 0xe,
    0xe2, 0x22, 0x22, 0x22, 0x2e, 0xeb, 0xbb, 0xbb,
    0xbb, 0xbe, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+56FE "图" */
    0xdc, 0xcc, 0xcc, 0xcc, 0xce, 0xd1, 0x4, 0xc2,
    0x21, 0xe, 0xd1, 0x2e, 0x99, 0xf5, 0xe, 0xd1,
    0xd9, 0xa7, 0x90, 0xe, 0xd1, 0x13, 0xde, 0x50,
    0xe, 0xd3, 0xd9, 0x85, 0x8d, 0x4e, 0xd1, 0x0,
    0x4b, 0x70, 0xe, 0xd1, 0x7, 0xd9, 0x30, 0xe,
    0xd2, 0x11, 0x16, 0x51, 0x1e, 0xdb, 0xbb, 0xbb,
    0xbb, 0xbe, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xde,
    0xee, 0xfe, 0xee, 0x40, 0x0, 0x0, 0x4, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xb0, 0x0, 0x0,
    0xa, 0xee, 0xee, 0xfe, 0xee, 0xe4, 0x0, 0x0,
    0x8, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xe, 0x89,
    0x0, 0x0, 0x0, 0x0, 0x99, 0xd, 0x40, 0x0,
    0x0, 0x8, 0xd0, 0x3, 0xe5, 0x0, 0x4, 0xcc,
    0x10, 0x0, 0x3e, 0xb4, 0x1b, 0x60, 0x0, 0x0,
    0x0, 0x73,

    /* U+59CB "始" */
    0x0, 0x63, 0x0, 0x7, 0x20, 0x0, 0x0, 0xa2,
    0x0, 0x1e, 0x10, 0x0, 0x4, 0xd4, 0x40, 0x95,
    0xa, 0x10, 0x19, 0xe9, 0xe3, 0xb0, 0x6, 0xb0,
    0x2, 0xa1, 0xcb, 0xec, 0xba, 0xc7, 0x6, 0x64,
    0x91, 0x0, 0x0, 0x0, 0xb, 0x48, 0x53, 0xdd,
    0xdd, 0xc0, 0x4, 0xce, 0x3, 0xb0, 0x0, 0xe0,
    0x0, 0x8d, 0xa3, 0xb0, 0x0, 0xe0, 0x4, 0xd0,
    0x64, 0xec, 0xcc, 0xe0, 0x1c, 0x20, 0x3, 0xb0,
    0x0, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5B58 "存" */
    0x0, 0x0, 0x8, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0x0, 0x0, 0x0, 0x9, 0xdc, 0xfd, 0xcc,
    0xcc, 0xc0, 0x0, 0x5, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0x1d, 0xcc, 0xce, 0x80, 0x1, 0xd8,
    0x0, 0x1, 0xaa, 0x10, 0x1c, 0xb8, 0x0, 0x8,
    0x80, 0x0, 0x5, 0x48, 0xac, 0xce, 0xec, 0xd4,
    0x0, 0x48, 0x0, 0x7, 0x60, 0x0, 0x0, 0x48,
    0x0, 0x8, 0x60, 0x0, 0x0, 0x59, 0x0, 0x9d,
    0x20, 0x0,

    /* U+5B89 "安" */
    0x0, 0x0, 0x9, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x80, 0x0, 0x0, 0x7, 0xec, 0xcc, 0xcc,
    0xce, 0x60, 0x6, 0x80, 0x39, 0x0, 0x8, 0x60,
    0x0, 0x0, 0x96, 0x0, 0x1, 0x10, 0xc, 0xcc,
    0xfc, 0xcc, 0xcc, 0xc2, 0x0, 0x9, 0x60, 0x5,
    0xb0, 0x0, 0x0, 0x2f, 0x94, 0x1d, 0x20, 0x0,
    0x0, 0x0, 0x3b, 0xfd, 0x30, 0x0, 0x0, 0x4,
    0xad, 0x45, 0xdc, 0x30, 0x9, 0xea, 0x40, 0x0,
    0x6, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5C4F "屏" */
    0x0, 0xfb, 0xbb, 0xbb, 0xbd, 0x60, 0x0, 0xea,
    0xaa, 0xaa, 0xad, 0x60, 0x0, 0xd0, 0x26, 0x0,
    0x62, 0x0, 0x0, 0xd0, 0xc, 0x0, 0xd1, 0x0,
    0x0, 0xd8, 0xce, 0xcc, 0xfc, 0xb0, 0x1, 0xb0,
    0xc, 0x0, 0xd0, 0x0, 0x4, 0xab, 0xbf, 0xbb,
    0xfb, 0xb3, 0x9, 0x60, 0xd, 0x0, 0xd0, 0x0,
    0x1e, 0x10, 0x89, 0x0, 0xd0, 0x0, 0x4, 0x6,
    0xc0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5E55 "幕" */
    0x0, 0x3, 0xb0, 0xe, 0x0, 0x0, 0x9, 0x9a,
    0xe9, 0x9f, 0x99, 0x80, 0x0, 0x36, 0xc5, 0x5e,
    0x52, 0x0, 0x0, 0xa7, 0x44, 0x44, 0x97, 0x0,
    0x0, 0xaa, 0x99, 0x99, 0xc6, 0x0, 0x0, 0x68,
    0xab, 0x88, 0x84, 0x0, 0x2a, 0x9b, 0xea, 0x9d,
    0xa9, 0xa0, 0x1, 0x7d, 0x45, 0x55, 0xd6, 0x20,
    0x4c, 0x8d, 0x7b, 0xb7, 0xba, 0xb3, 0x0, 0x3b,
    0x6, 0x60, 0x95, 0x0, 0x0, 0x26, 0x7, 0x74,
    0xa1, 0x0,

    /* U+5E76 "并" */
    0x0, 0x3, 0x0, 0x0, 0x61, 0x0, 0x0, 0xa,
    0xa0, 0x2, 0xf2, 0x0, 0x1, 0x23, 0xe3, 0x27,
    0xb2, 0x20, 0x5, 0xbb, 0xfb, 0xbc, 0xeb, 0xb0,
    0x0, 0x0, 0xd0, 0x3, 0xb0, 0x0, 0x0, 0x0,
    0xd0, 0x3, 0xb0, 0x0, 0x2e, 0xdd, 0xfd, 0xde,
    0xfd, 0xdb, 0x0, 0x4, 0xa0, 0x3, 0xb0, 0x0,
    0x0, 0xb, 0x60, 0x3, 0xb0, 0x0, 0x0, 0x9c,
    0x0, 0x3, 0xb0, 0x0, 0x8, 0xc1, 0x0, 0x3,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5F00 "开" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xed,
    0xfd, 0xdf, 0xed, 0x70, 0x0, 0x0, 0xe1, 0xc,
    0x30, 0x0, 0x0, 0x0, 0xe1, 0xc, 0x30, 0x0,
    0x0, 0x0, 0xe1, 0xc, 0x30, 0x0, 0x5e, 0xdc,
    0xfd, 0xcf, 0xdd, 0xe1, 0x0, 0x0, 0xf0, 0xc,
    0x30, 0x0, 0x0, 0x5, 0xb0, 0xc, 0x30, 0x0,
    0x0, 0xd, 0x50, 0xc, 0x30, 0x0, 0x1, 0xca,
    0x0, 0xc, 0x30, 0x0, 0x8, 0x90, 0x0, 0xc,
    0x40, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf2, 0x71, 0x0, 0x0, 0x0, 0x0, 0xe2,
    0xaa, 0x0, 0x1, 0x11, 0x11, 0xe3, 0x27, 0x10,
    0x4c, 0xcc, 0xcc, 0xec, 0xcc, 0xb0, 0x0, 0x0,
    0x0, 0xa5, 0x0, 0x0, 0xa, 0xcd, 0xdb, 0x78,
    0x0, 0x0, 0x0, 0xd, 0x10, 0x2d, 0x0, 0x0,
    0x0, 0xd, 0x10, 0xd, 0x30, 0x0, 0x0, 0xd,
    0x47, 0x36, 0xb0, 0x62, 0x8, 0xbd, 0x95, 0x10,
    0xd8, 0xd5, 0x8, 0x30, 0x0, 0x0, 0x1c, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6253 "打" */
    0x0, 0x3d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0x5, 0x88, 0x88, 0x84, 0x0, 0x2c, 0x4, 0x77,
    0xe8, 0x73, 0xd, 0xdf, 0xc5, 0x0, 0xd2, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0xd2, 0x0, 0x0, 0x2d,
    0x73, 0x0, 0xd2, 0x0, 0x16, 0xbe, 0x70, 0x0,
    0xd2, 0x0, 0x8, 0x5c, 0x0, 0x0, 0xd2, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0xd2, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0xe2, 0x0, 0x5, 0xd9, 0x0, 0x2c,
    0xe0, 0x0, 0x1, 0x20, 0x0, 0x4, 0x10, 0x0,

    /* U+626B "扫" */
    0x0, 0x3c, 0x0, 0x11, 0x11, 0x10, 0x0, 0x3c,
    0x8, 0xff, 0xff, 0xf1, 0x3, 0x5c, 0x21, 0x0,
    0x0, 0xd1, 0x2b, 0xce, 0xb5, 0x0, 0x0, 0xd1,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0xd1, 0x0, 0x3e,
    0xc1, 0xcc, 0xcc, 0xf1, 0x17, 0xdd, 0x10, 0x0,
    0x0, 0xd1, 0x7, 0x3c, 0x0, 0x0, 0x0, 0xd1,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0xd1, 0x0, 0x3c,
    0xb, 0xdd, 0xdd, 0xf1, 0x7, 0xd8, 0x0, 0x0,
    0x0, 0xd1, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0xd0, 0x6b, 0xbf,
    0xbb, 0xa0, 0x23, 0xd3, 0x34, 0x54, 0x76, 0x40,
    0x59, 0xfa, 0x16, 0x90, 0x87, 0x0, 0x0, 0xd0,
    0x57, 0xb7, 0xe8, 0x70, 0x0, 0xda, 0x33, 0x87,
    0x33, 0x30, 0x5d, 0xf1, 0xbc, 0xed, 0xcc, 0xc3,
    0x10, 0xd0, 0x5, 0x90, 0x85, 0x0, 0x0, 0xd0,
    0x6, 0xd9, 0xc0, 0x0, 0x1, 0xe0, 0x1, 0x7c,
    0xca, 0x20, 0xd, 0x90, 0xac, 0x60, 0x6, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63CF "描" */
    0x0, 0xe0, 0x0, 0xa0, 0x89, 0x0, 0x0, 0xe0,
    0x12, 0xb2, 0x99, 0x21, 0x0, 0xe0, 0x7b, 0xeb,
    0xde, 0xb7, 0x6c, 0xfc, 0x20, 0xa0, 0x89, 0x0,
    0x0, 0xe0, 0x0, 0x10, 0x11, 0x0, 0x0, 0xe8,
    0x2f, 0xaf, 0xaa, 0xe0, 0x5d, 0xf3, 0xd, 0xd,
    0x0, 0xe0, 0x10, 0xe0, 0xf, 0xcf, 0xcc, 0xe0,
    0x0, 0xe0, 0xd, 0xd, 0x0, 0xe0, 0x0, 0xe0,
    0xd, 0x1d, 0x11, 0xe0, 0xa, 0x90, 0x1f, 0xaa,
    0xaa, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6536 "收" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x20, 0x5d, 0x0, 0x0, 0x4, 0xb, 0x10, 0xb6,
    0x0, 0x0, 0xe, 0xb, 0x11, 0xfd, 0xcc, 0xd8,
    0xe, 0xb, 0x19, 0x80, 0xd, 0x0, 0xe, 0xb,
    0x4e, 0xd3, 0xd, 0x0, 0xe, 0xb, 0x34, 0x87,
    0x3a, 0x0, 0xe, 0x3d, 0x10, 0x2c, 0x86, 0x0,
    0x3e, 0x9d, 0x10, 0xc, 0xe1, 0x0, 0x0, 0xb,
    0x10, 0x1d, 0xe1, 0x0, 0x0, 0xb, 0x14, 0xd8,
    0x5d, 0x60, 0x0, 0xc, 0x7e, 0x40, 0x3, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6587 "文" */
    0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0x0, 0x0, 0x0, 0x11, 0x11, 0x1a, 0x31,
    0x11, 0x10, 0x7c, 0xde, 0xcc, 0xce, 0xec, 0xd1,
    0x0, 0x3b, 0x0, 0xb, 0x40, 0x0, 0x0, 0xd,
    0x10, 0xe, 0x0, 0x0, 0x0, 0x7, 0x80, 0x79,
    0x0, 0x0, 0x0, 0x0, 0xd5, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x7,
    0xc5, 0xba, 0x30, 0x0, 0x3b, 0xd8, 0x0, 0x5,
    0xce, 0xa0, 0x4, 0x0, 0x0, 0x0, 0x2, 0x20,

    /* U+672C "本" */
    0x0, 0x0, 0x4, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xb0, 0x0, 0x0, 0x1, 0x22, 0x25, 0xb2,
    0x22, 0x20, 0x7, 0xaa, 0xbf, 0xfd, 0xaa, 0xb2,
    0x0, 0x0, 0x6c, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0xd5, 0xba, 0x40, 0x0, 0x0, 0x9, 0x74, 0xb2,
    0xe1, 0x0, 0x0, 0x7b, 0x4, 0xb0, 0x5d, 0x20,
    0xa, 0xc6, 0xdd, 0xed, 0xb5, 0xe4, 0x5, 0x0,
    0x4, 0xb0, 0x0, 0x20, 0x0, 0x0, 0x4, 0xb0,
    0x0, 0x0,

    /* U+673A "机" */
    0x0, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc2,
    0x1, 0xec, 0xe1, 0x0, 0x0, 0xc2, 0x1, 0xc0,
    0xd1, 0x0, 0x6c, 0xfc, 0xc1, 0xc0, 0xd1, 0x0,
    0x2, 0xf2, 0x1, 0xc0, 0xd1, 0x0, 0x7, 0xfa,
    0x32, 0xb0, 0xd1, 0x0, 0xd, 0xd6, 0xd3, 0xa0,
    0xd1, 0x0, 0x7a, 0xc2, 0x15, 0x80, 0xd1, 0x0,
    0x61, 0xc2, 0x9, 0x50, 0xd1, 0x0, 0x0, 0xc2,
    0x2d, 0x0, 0xd1, 0xb0, 0x0, 0xd3, 0xb4, 0x0,
    0xad, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6821 "校" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0xd1,
    0x0, 0x1e, 0x0, 0x0, 0x0, 0xd1, 0x0, 0x8,
    0x20, 0x0, 0x0, 0xd1, 0xae, 0xee, 0xee, 0xe1,
    0x6c, 0xfc, 0x10, 0x40, 0x12, 0x0, 0x4, 0xf1,
    0x9, 0xa0, 0x3e, 0x40, 0x9, 0xfa, 0x4b, 0x0,
    0x3, 0x90, 0x1d, 0xd9, 0x44, 0x80, 0x88, 0x0,
    0x98, 0xd1, 0x0, 0xd3, 0xe1, 0x0, 0x11, 0xd1,
    0x0, 0x5f, 0x60, 0x0, 0x0, 0xd1, 0x4, 0xda,
    0xd5, 0x0, 0x0, 0xd3, 0xda, 0x20, 0x3a, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6E90 "源" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xa1,
    0xfc, 0xcc, 0xcc, 0xd4, 0x0, 0x72, 0xd0, 0x9,
    0x50, 0x0, 0x16, 0x0, 0xd2, 0xfc, 0xcc, 0xa0,
    0x18, 0xc0, 0xd2, 0xe5, 0x57, 0xa0, 0x0, 0x1,
    0xd2, 0xe5, 0x57, 0xa0, 0x1, 0x92, 0xc2, 0xec,
    0xdc, 0x90, 0x5, 0xa4, 0xa0, 0x1, 0xd0, 0x0,
    0xa, 0x59, 0x66, 0xb1, 0xd5, 0x90, 0x1f, 0x2e,
    0x4e, 0x21, 0xd0, 0xc5, 0x28, 0x68, 0x44, 0xb,
    0xb0, 0x11, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,

    /* U+7248 "版" */
    0xb, 0x2d, 0x4, 0xca, 0x98, 0x60, 0xb, 0x1c,
    0x4, 0x90, 0x0, 0x0, 0xb, 0x1c, 0x3, 0xa4,
    0x44, 0x20, 0xb, 0xbb, 0xb4, 0xdd, 0x7b, 0x60,
    0xb, 0x10, 0x4, 0x8c, 0x9, 0x30, 0xb, 0xbc,
    0x35, 0x7d, 0xc, 0x10, 0xc, 0x9, 0x38, 0x59,
    0x5c, 0x0, 0xd, 0x9, 0x3c, 0x12, 0xf7, 0x0,
    0x2b, 0x9, 0x6c, 0x6, 0xf9, 0x0, 0x76, 0x9,
    0x74, 0xac, 0x19, 0xd1, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x20,

    /* U+7535 "电" */
    0x0, 0x1, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0x0, 0x0, 0x0, 0xbc, 0xcd, 0xfc, 0xcc, 0x90,
    0xe, 0x20, 0x1d, 0x0, 0x3b, 0x0, 0xe2, 0x1,
    0xd0, 0x4, 0xb0, 0xe, 0xcb, 0xbf, 0xbb, 0xcb,
    0x0, 0xe2, 0x1, 0xd0, 0x3, 0xb0, 0xe, 0xdc,
    0xdf, 0xcc, 0xdb, 0x0, 0xe2, 0x1, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0x0, 0x0, 0xb5, 0x0,
    0x0, 0xbd, 0xcc, 0xdd, 0x10,

    /* U+753B "画" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xcc, 0xcc,
    0xcc, 0xcc, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xe, 0xbb, 0xeb, 0xd0, 0x50, 0xe0, 0xd0,
    0x2b, 0xd, 0xf, 0xe, 0xd, 0xcc, 0xec, 0xd0,
    0xf0, 0xe0, 0xd0, 0x2b, 0xd, 0xf, 0xe, 0xd,
    0x57, 0xc5, 0xd0, 0xf0, 0xe0, 0x45, 0x55, 0x54,
    0xf, 0xe, 0x99, 0x99, 0x99, 0x99, 0xf0, 0x11,
    0x11, 0x11, 0x11, 0x1f, 0x0,

    /* U+7CFB "系" */
    0x0, 0x0, 0x0, 0x24, 0x6a, 0x40, 0x4, 0xdc,
    0xcf, 0xa8, 0x64, 0x30, 0x0, 0x0, 0x4e, 0x30,
    0x51, 0x0, 0x0, 0x17, 0xb1, 0xa, 0xb3, 0x0,
    0x0, 0x5c, 0xbe, 0xd4, 0x10, 0x0, 0x0, 0x3,
    0xa7, 0x0, 0x7b, 0x10, 0x1, 0xff, 0xcb, 0xcb,
    0xbc, 0xd2, 0x0, 0x11, 0x0, 0xe0, 0x10, 0x40,
    0x0, 0x7c, 0x0, 0xe0, 0x8c, 0x10, 0x9, 0xb0,
    0x0, 0xe0, 0x6, 0xe1, 0x5, 0x0, 0x6f, 0x90,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EBF "线" */
    0x0, 0x51, 0x0, 0x62, 0x0, 0x0, 0x1, 0xd1,
    0x0, 0xb4, 0xa5, 0x0, 0x8, 0x53, 0x30, 0xa4,
    0x15, 0x20, 0x4b, 0xc, 0x65, 0xdd, 0xba, 0x50,
    0x8c, 0xd9, 0x37, 0xa5, 0x0, 0x40, 0x0, 0xb0,
    0x15, 0xbd, 0xcb, 0x91, 0xa, 0x73, 0x47, 0x7a,
    0x9, 0x30, 0x3d, 0xa9, 0x10, 0xd, 0x9b, 0x10,
    0x0, 0x15, 0x50, 0xd, 0xc0, 0x0, 0x6e, 0xb7,
    0x34, 0xd7, 0xd4, 0xa4, 0x0, 0x0, 0x7c, 0x20,
    0x2d, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x30, 0x0, 0x9, 0x0, 0x0, 0x1, 0xe2,
    0x0, 0x9, 0x20, 0x0, 0x8, 0x72, 0x6c, 0xcc,
    0xcc, 0xc0, 0x3d, 0x3d, 0x50, 0x88, 0x0, 0x0,
    0x59, 0xca, 0x5, 0xa0, 0x2d, 0x10, 0x1, 0xd1,
    0x3f, 0xbb, 0xcd, 0xc0, 0x1c, 0xb9, 0x54, 0xc3,
    0xe0, 0x30, 0x1a, 0x74, 0x10, 0xc1, 0xe0, 0x0,
    0x0, 0x4, 0x51, 0xd0, 0xe0, 0x0, 0x4c, 0xea,
    0x4a, 0x70, 0xe0, 0xa4, 0x14, 0x0, 0xaa, 0x0,
    0xbd, 0xd1, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+80CC "背" */
    0x0, 0x0, 0xd0, 0x2c, 0x0, 0x10, 0x1a, 0xaa,
    0xd0, 0x1d, 0x9d, 0x70, 0x1, 0x0, 0xd0, 0x1c,
    0x20, 0x0, 0x49, 0xbc, 0xd0, 0x1d, 0x11, 0xa3,
    0x24, 0x0, 0x80, 0x7, 0x99, 0x80, 0x0, 0x9b,
    0xbb, 0xbb, 0xb9, 0x0, 0x0, 0xb4, 0x22, 0x22,
    0x3c, 0x0, 0x0, 0xb9, 0x88, 0x88, 0x8c, 0x0,
    0x0, 0xbb, 0xbb, 0xbb, 0xbc, 0x0, 0x0, 0xb2,
    0x0, 0x0, 0x2c, 0x0, 0x0, 0xc2, 0x0, 0x5,
    0xd6, 0x0,

    /* U+81EA "自" */
    0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0x6, 0x90,
    0x0, 0x0, 0xe, 0xcd, 0xdc, 0xcc, 0xe0, 0xe,
    0x0, 0x0, 0x0, 0xe0, 0xf, 0xcc, 0xcc, 0xcc,
    0xf0, 0xe, 0x0, 0x0, 0x0, 0xe0, 0xe, 0x11,
    0x11, 0x11, 0xe0, 0xf, 0xaa, 0xaa, 0xaa, 0xf0,
    0xe, 0x0, 0x0, 0x0, 0xe0, 0xf, 0xcc, 0xcc,
    0xcc, 0xf0, 0xe, 0x0, 0x0, 0x0, 0xe0,

    /* U+8702 "蜂" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x67,
    0x0, 0x1e, 0x20, 0x0, 0x0, 0x66, 0x0, 0x8e,
    0xbd, 0x80, 0x9, 0xcc, 0xa4, 0xbc, 0x4c, 0x0,
    0xd, 0x77, 0xd2, 0x19, 0xf9, 0x20, 0xd, 0x66,
    0xd8, 0xd7, 0x76, 0xd9, 0xe, 0x99, 0xe1, 0x66,
    0xe6, 0x60, 0xe, 0xaa, 0xd0, 0x55, 0xe5, 0x50,
    0x0, 0x66, 0x50, 0xaa, 0xfa, 0x90, 0x0, 0x67,
    0xd2, 0x0, 0xd0, 0x0, 0x3b, 0xc9, 0xa9, 0xaa,
    0xfa, 0xa2, 0x2, 0x0, 0x0, 0x0, 0xe0, 0x0,

    /* U+88C5 "装" */
    0x0, 0x0, 0xb0, 0x6, 0x80, 0x0, 0x5, 0x40,
    0xd0, 0x6, 0x80, 0x0, 0x3, 0xd1, 0xd4, 0xbc,
    0xda, 0x90, 0x0, 0x16, 0xd0, 0x5, 0x70, 0x0,
    0x9, 0xb4, 0xd0, 0xbb, 0xbb, 0x40, 0x1, 0x0,
    0x58, 0x50, 0x0, 0x0, 0xb, 0xbb, 0xbc, 0xcb,
    0xbb, 0xc0, 0x0, 0x0, 0x98, 0xc0, 0x7, 0x0,
    0x1, 0x6d, 0xb0, 0x4b, 0xb7, 0x0, 0xb, 0x63,
    0xca, 0x36, 0xd5, 0x10, 0x0, 0x3, 0xb3, 0x0,
    0x28, 0xa0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x6, 0x0, 0x0, 0x0, 0x11, 0x11,
    0xc5, 0x11, 0x11, 0xb, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa0, 0x4, 0x55, 0x55, 0x55, 0x30, 0x0, 0x34,
    0x44, 0x44, 0x43, 0x0, 0x9, 0xbb, 0xbb, 0xbb,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xbb, 0xbb, 0xbb, 0x80, 0x0, 0xa3, 0x0, 0x0,
    0x59, 0x0, 0xa, 0xba, 0xaa, 0xac, 0x90, 0x0,
    0xb5, 0x11, 0x11, 0x6a, 0x0,

    /* U+8BAE "议" */
    0x2, 0x50, 0x0, 0x83, 0x0, 0x0, 0x1, 0xe1,
    0x0, 0x96, 0xb, 0x10, 0x0, 0x62, 0x78, 0x5a,
    0x1e, 0x0, 0x0, 0x0, 0x4a, 0x2c, 0x4a, 0x0,
    0x7c, 0xf0, 0xe, 0x0, 0x87, 0x0, 0x0, 0xe0,
    0xa, 0x50, 0xc3, 0x0, 0x0, 0xe0, 0x3, 0xd2,
    0xd0, 0x0, 0x0, 0xe0, 0x60, 0xae, 0x70, 0x0,
    0x0, 0xeb, 0x70, 0x8f, 0x90, 0x0, 0x1, 0xf8,
    0x1a, 0xc1, 0xbd, 0x50, 0x3, 0xa2, 0xea, 0x0,
    0x6, 0xd1, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+8BED "语" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x9a, 0xcd, 0xaa, 0xa0, 0x0, 0x20, 0x5b, 0xdd,
    0xaa, 0x30, 0x35, 0x50, 0x0, 0xc3, 0xc, 0x30,
    0x59, 0xf0, 0x0, 0xe0, 0xd, 0x20, 0x0, 0xe2,
    0xcb, 0xdb, 0xbd, 0xb6, 0x0, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe2, 0xf, 0xcc, 0xce, 0x60,
    0x0, 0xed, 0x3e, 0x0, 0x7, 0x60, 0x0, 0xf5,
    0xe, 0x11, 0x18, 0x60, 0x3, 0x70, 0xf, 0xbb,
    0xbd, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8C31 "谱" */
    0x0, 0x0, 0x3, 0x30, 0x36, 0x0, 0x9, 0x70,
    0x2, 0xc0, 0x95, 0x0, 0x0, 0xc1, 0xaa, 0xeb,
    0xfa, 0xa3, 0x0, 0x0, 0x76, 0xb1, 0xd3, 0xb0,
    0x5b, 0xa0, 0x17, 0xb1, 0xd5, 0x30, 0x0, 0xd5,
    0xaa, 0xaa, 0xaa, 0xa6, 0x0, 0xd0, 0x6, 0x66,
    0x66, 0x30, 0x0, 0xd0, 0xe, 0x44, 0x49, 0x70,
    0x0, 0xd8, 0xf, 0xbb, 0xbd, 0x70, 0x0, 0xf9,
    0xe, 0x0, 0x7, 0x70, 0x1, 0x80, 0xe, 0xaa,
    0xac, 0x70,

    /* U+8F93 "输" */
    0x1, 0x80, 0x0, 0x59, 0x0, 0x0, 0x4, 0xb0,
    0x0, 0xda, 0x60, 0x0, 0x7e, 0xdc, 0x1b, 0x50,
    0xb9, 0x0, 0xb, 0x31, 0xb9, 0xcc, 0xb8, 0xd4,
    0xc, 0xb4, 0xaa, 0xa4, 0x0, 0x80, 0x8b, 0xda,
    0x77, 0x76, 0xc2, 0xc0, 0x56, 0xc5, 0x7c, 0xd6,
    0xc1, 0xc0, 0x0, 0xa5, 0x75, 0x66, 0xc1, 0xc0,
    0x6b, 0xfb, 0x7d, 0xd6, 0xc1, 0xc0, 0x22, 0xb3,
    0x75, 0x66, 0xa1, 0xc0, 0x0, 0xb3, 0x76, 0xc4,
    0xb, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9000 "退" */
    0x5, 0x80, 0xbd, 0xcc, 0xcf, 0x0, 0x2, 0xe0,
    0xb4, 0x0, 0xe, 0x0, 0x0, 0xb1, 0xbd, 0xcc,
    0xcf, 0x0, 0x0, 0x0, 0xb4, 0x0, 0xe, 0x0,
    0x4d, 0xc0, 0xbd, 0xcc, 0xcc, 0x0, 0x1, 0xe0,
    0xb4, 0x72, 0xa, 0x50, 0x0, 0xe0, 0xb4, 0x6e,
    0xb8, 0x0, 0x0, 0xe0, 0xb5, 0x83, 0xd9, 0x0,
    0x0, 0xe0, 0xee, 0x70, 0x1c, 0x90, 0x8, 0xfc,
    0xa2, 0x0, 0x1, 0x41, 0x4b, 0x14, 0x9d, 0xef,
    0xfe, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9053 "道" */
    0x0, 0x10, 0x6, 0x30, 0x64, 0x0, 0x7, 0x90,
    0x2, 0x80, 0xc1, 0x0, 0x2, 0xd1, 0xcc, 0xcf,
    0xcc, 0xc4, 0x0, 0x0, 0x5, 0x5e, 0x55, 0x10,
    0x13, 0x30, 0x1e, 0x66, 0x6c, 0x30, 0x38, 0xe0,
    0x1f, 0xbb, 0xbe, 0x30, 0x0, 0xe0, 0x1e, 0x33,
    0x3b, 0x30, 0x0, 0xe0, 0x1e, 0x66, 0x6c, 0x30,
    0x0, 0xe0, 0x2f, 0xbb, 0xbe, 0x30, 0xa, 0xdc,
    0x41, 0x0, 0x0, 0x10, 0x3a, 0x2, 0xad, 0xee,
    0xff, 0xf1,

    /* U+95ED "闭" */
    0x7, 0x0, 0x0, 0x0, 0x0, 0x8, 0xa0, 0xcd,
    0xdd, 0xdf, 0x50, 0x20, 0x7, 0x30, 0xe, 0xf0,
    0x0, 0xb, 0x30, 0xe, 0xe6, 0xed, 0xdf, 0xee,
    0xe, 0xe0, 0x0, 0xae, 0x30, 0xe, 0xe0, 0x5,
    0xbb, 0x30, 0xe, 0xe0, 0x4e, 0x1b, 0x30, 0xe,
    0xe7, 0xd2, 0xb, 0x30, 0xe, 0xe1, 0x0, 0x8f,
    0x20, 0x1e, 0xf0, 0x0, 0x42, 0x7, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+9891 "频" */
    0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x7, 0x2d,
    0x1, 0xbb, 0xeb, 0xb2, 0xa, 0x3d, 0xb8, 0x2,
    0xa0, 0x0, 0xa, 0x3d, 0x0, 0x7c, 0xdb, 0xc0,
    0x4d, 0xbd, 0xba, 0x94, 0x10, 0xd0, 0x2, 0x2c,
    0x0, 0x94, 0xd2, 0xd0, 0xa, 0x4d, 0x3b, 0x94,
    0xd0, 0xd0, 0x1c, 0xd, 0xa5, 0x94, 0xe0, 0xd0,
    0x0, 0xe, 0x90, 0x45, 0xb0, 0x80, 0x3, 0xb9,
    0x0, 0x2d, 0x3b, 0x60, 0x2c, 0x50, 0x5, 0xd4,
    0x0, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9E23 "鸣" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x3,
    0x7c, 0x44, 0x30, 0xfc, 0xf0, 0xb8, 0x76, 0x8b,
    0xd, 0xc, 0xa, 0x2d, 0x33, 0xa0, 0xd0, 0xc0,
    0xa2, 0x35, 0x98, 0xd, 0xc, 0xa, 0x20, 0x98,
    0x10, 0xd0, 0xc0, 0xad, 0xcc, 0xcd, 0x4e, 0xbf,
    0x0, 0x0, 0x0, 0xa3, 0xd0, 0xa5, 0xdc, 0xcc,
    0x6a, 0x26, 0x0, 0x0, 0x0, 0x0, 0xd1, 0x0,
    0x0, 0x0, 0x8, 0xeb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 96, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 96, .box_w = 2, .box_h = 9, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9, .adv_w = 96, .box_w = 4, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 15, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 42, .adv_w = 96, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 78, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 105, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 132, .adv_w = 96, .box_w = 2, .box_h = 3, .ofs_x = 2, .ofs_y = 6},
    {.bitmap_index = 135, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 159, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 183, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 204, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 222, .adv_w = 96, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 228, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 231, .adv_w = 96, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 234, .adv_w = 96, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 267, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 297, .adv_w = 96, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 311, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 341, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 368, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 395, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 422, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 449, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 476, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 503, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 530, .adv_w = 96, .box_w = 2, .box_h = 6, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 536, .adv_w = 96, .box_w = 2, .box_h = 8, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 544, .adv_w = 96, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 577, .adv_w = 96, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 589, .adv_w = 96, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 622, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 649, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 676, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 703, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 730, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 757, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 784, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 811, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 838, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 865, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 892, .adv_w = 96, .box_w = 2, .box_h = 9, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 901, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 928, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 955, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 982, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1009, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1036, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1063, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1090, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1120, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1147, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1174, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1201, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1228, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1255, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1282, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1309, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1336, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1363, .adv_w = 96, .box_w = 4, .box_h = 11, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1385, .adv_w = 96, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1418, .adv_w = 96, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1440, .adv_w = 96, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 1446, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1449, .adv_w = 96, .box_w = 4, .box_h = 3, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 1455, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1473, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1500, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1518, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1545, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1563, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1590, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1617, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1644, .adv_w = 96, .box_w = 2, .box_h = 9, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1653, .adv_w = 96, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1675, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1702, .adv_w = 96, .box_w = 2, .box_h = 9, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1711, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1729, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1747, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1765, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1789, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1813, .adv_w = 96, .box_w = 4, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1825, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1843, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1867, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1885, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1903, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1921, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1939, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1963, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1981, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2005, .adv_w = 96, .box_w = 2, .box_h = 13, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 2018, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2042, .adv_w = 192, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2097, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2175, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2247, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2319, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2385, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2451, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2523, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2595, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2661, .adv_w = 192, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2716, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2788, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2860, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2932, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2998, .adv_w = 192, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3064, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3130, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3196, .adv_w = 192, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3251, .adv_w = 192, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3306, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3372, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3444, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3510, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3582, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3648, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3714, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3786, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3852, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3930, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4002, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4074, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4152, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4224, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4302, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4374, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4440, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4512, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4590, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4662, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4728, .adv_w = 192, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4789, .adv_w = 192, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4850, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4922, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4994, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5066, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5132, .adv_w = 192, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5187, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5259, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5325, .adv_w = 192, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5386, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5458, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5530, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5596, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5668, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5740, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5806, .adv_w = 192, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5866, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5938, .adv_w = 192, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -2}};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xc9, 0xf3, 0x16e, 0x1b0, 0x1b4, 0x31c, 0x346,
    0x399, 0x3cd, 0x409, 0x47b, 0x4d8, 0x522, 0x55e, 0x5ca,
    0x83b, 0x8cd, 0x8d1, 0xafc, 0xb9e, 0xd2b, 0xd5c, 0xe22,
    0x1028, 0x1049, 0x10d3, 0x10e2, 0x1426, 0x143e, 0x1578, 0x15a2,
    0x1709, 0x175a, 0x18ff, 0x190d, 0x19f4, 0x2063, 0x241b, 0x2708,
    0x270e, 0x2ece, 0x3092, 0x30b2, 0x329f, 0x33bd, 0x38d5, 0x3a98,
    0x3bd3, 0x3d81, 0x3dc0, 0x3e04, 0x4166, 0x41d3, 0x4226, 0x47c0,
    0x4a64, 0x4ff6};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
    {
        {.range_start = 32, .range_length = 94, .glyph_id_start = 1, .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY},
        {.range_start = 20013, .range_length = 20471, .glyph_id_start = 95, .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 58, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY}};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_chinese_12 = {
#else
lv_font_t lv_font_chinese_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt, /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt, /*Function pointer to get glyph's bitmap*/
    .line_height = 13,                              /*The maximum line height required by the font*/
    .base_line = 2,                                 /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_CHINESE_12*/
