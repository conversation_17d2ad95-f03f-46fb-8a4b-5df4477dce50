#ifndef __page_main_H
#define __page_main_H

#include "page_about.h"
#include "page_menu.h"
#include "page_scan_calib.h"
#include "page_scan_chart.h"
#include "page_scan_table.h"
#include "page_scan.h"
#include "page_setup.h"

#ifdef __cplusplus
extern "C" {
#endif

    /*********************
    *      INCLUDES
    *********************/
#include "../lvgl/lvgl.h"

    /*********************
    *      DEFINES
    *********************/

    /**********************
    *      TYPEDEFS
    **********************/
   typedef enum
    {
        fre_single = 0,
        fre_ten,
        fre_hunderd,
        fre_thousand,
        fre_label_count,
    }fre_label_enum;
		
		    typedef enum
    {
        fre_pre = 0,
        fre_cur,
        fre_pre_cur_count,
    }fre_pre_cur;
    /**********************
    * GLOBAL PROTOTYPES
    **********************/
void page_main_create(void);
void key_delay_time(uint32_t time_out ) ;

// 声明外部可见的变量
extern lv_obj_t *lv_channel_label;


// void page_main_create(void);
// void fre_label_update(uint8_t a, uint8_t b);


extern void fre_label_update(uint8_t a, uint8_t b);
/**********************
 *      MACROS
 **********************/
extern bool lock_flag;
extern bool menu_flag;
extern lv_timer_t *page_mian_time;
#ifdef __cplusplus
} /*extern "C"*/
#endif




#endif
