<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.toolchain.gnu.mingw.base.50437754">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.mingw.base.50437754" moduleId="org.eclipse.cdt.core.settings" name="Default">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GNU_PE64" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration buildProperties="" id="cdt.managedbuild.toolchain.gnu.mingw.base.50437754" name="Default" parent="org.eclipse.cdt.build.core.emptycfg">
					<folderInfo id="cdt.managedbuild.toolchain.gnu.mingw.base.50437754.914130565" name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.mingw.base.570267770" name="MinGW GCC" superClass="cdt.managedbuild.toolchain.gnu.mingw.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_PE64" id="cdt.managedbuild.target.gnu.platform.mingw.base.1361618478" name="Debug Platform" osList="win32" superClass="cdt.managedbuild.target.gnu.platform.mingw.base"/>
							<builder buildPath="${workspace_loc:/RX5808}/Default" id="cdt.managedbuild.tool.gnu.builder.mingw.base.469050098" managedBuildOn="false" name="CDT Internal Builder.Default" superClass="cdt.managedbuild.tool.gnu.builder.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.assembler.mingw.base.2014291409" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.mingw.base.1489360778" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.mingw.base.226278779" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.mingw.base.1312453812" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.mingw.base.1545467979" name="MinGW C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.mingw.base.841012108" name="MinGW C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.mingw.base"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="RX5808.null.**********" name="RX5808"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
</cproject>