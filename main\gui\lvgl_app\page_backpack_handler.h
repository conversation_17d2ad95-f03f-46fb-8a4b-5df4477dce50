/**
 * @file page_backpack_handler.h
 * @brief 背包页面处理模块头文件
 *
 * 定义了背包通信相关的UI处理函数接口
 */

#ifndef PAGE_BACKPACK_HANDLER_H
#define PAGE_BACKPACK_HANDLER_H

#include <stdint.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

// 消息类型定义
typedef enum
{
    MSG_SET_BAND_CHANNEL,
    MSG_SET_FREQUENCY,
    MSG_UPDATE_BACKPACK_INFO 
} backpack_msg_type_t;

typedef struct
{
    backpack_msg_type_t type;
    uint8_t band, channel;
    uint16_t frequency;    // 频率值
    char version[64];      // 背包版本字符串
    bool version_received; // 版本接收标志
    uint8_t status_flags;  // 状态标志
    uint8_t uid[6];        // 背包UID
    bool status_received;  // 状态接收标志
} backpack_msg_t;

extern QueueHandle_t backpack_msg_queue;
extern backpack_msg_t current_backpack_info;

void backpack_handler_init(void);
void page_backpack_set_band_channel(uint8_t band, uint8_t channel);
bool backpack_set_frequency(uint16_t frequency);

const char *page_backpack_get_version(void);
bool page_backpack_is_version_received(void);
uint8_t page_backpack_get_status_flags(void);
const uint8_t *page_backpack_get_uid(void);
bool page_backpack_is_status_received(void);
void page_backpack_update_info(const char *version, bool version_received,
                               uint8_t status_flags, const uint8_t *uid, bool status_received);

#endif // PAGE_BACKPACK_HANDLER_H
