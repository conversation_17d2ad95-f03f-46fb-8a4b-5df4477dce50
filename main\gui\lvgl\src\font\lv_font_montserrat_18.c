/*******************************************************************************
 * Size: 18 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 18 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_18.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_18
    #define LV_FONT_MONTSERRAT_18 1
#endif

#if LV_FONT_MONTSERRAT_18

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x9f, 0x68, 0xf6, 0x8f, 0x57, 0xf4, 0x6f, 0x46,
    0xf3, 0x5f, 0x35, 0xf2, 0x4f, 0x10, 0x0, 0x15,
    0xa, 0xf7, 0x7f, 0x40,

    /* U+0022 "\"" */
    0xda, 0x9, 0xed, 0x90, 0x9d, 0xd9, 0x8, 0xdc,
    0x80, 0x8d, 0xc8, 0x8, 0xc0, 0x0, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0xe5, 0x0, 0x4f, 0x0, 0x0, 0x0,
    0xf, 0x30, 0x6, 0xe0, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x8c, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x5, 0x5a, 0xe5, 0x55, 0xda, 0x55,
    0x0, 0x0, 0x9b, 0x0, 0xe, 0x60, 0x0, 0x0,
    0xb, 0x90, 0x0, 0xf4, 0x0, 0x0, 0x11, 0xd8,
    0x11, 0x3f, 0x31, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x2, 0x45, 0xf6, 0x44, 0x8e, 0x44,
    0x20, 0x0, 0x2f, 0x10, 0x7, 0xc0, 0x0, 0x0,
    0x4, 0xf0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x5e,
    0x0, 0xb, 0x90, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0xe, 0x40, 0x0,
    0x0, 0x2, 0xad, 0xff, 0xd9, 0x20, 0x3, 0xff,
    0xbf, 0xcd, 0xfc, 0x0, 0xbf, 0x40, 0xe4, 0x3,
    0x30, 0xe, 0xd0, 0xe, 0x40, 0x0, 0x0, 0xdf,
    0x30, 0xe4, 0x0, 0x0, 0x5, 0xff, 0xaf, 0x50,
    0x0, 0x0, 0x4, 0xbf, 0xff, 0xd7, 0x0, 0x0,
    0x0, 0x1e, 0xbe, 0xfb, 0x0, 0x0, 0x0, 0xe4,
    0xb, 0xf4, 0x0, 0x0, 0xe, 0x40, 0x6f, 0x60,
    0xb4, 0x0, 0xe4, 0xb, 0xf3, 0x1e, 0xfd, 0xaf,
    0xbe, 0xfa, 0x0, 0x17, 0xcf, 0xff, 0xc6, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xe4, 0x0, 0x0,

    /* U+0025 "%" */
    0x1, 0xbf, 0xc3, 0x0, 0x0, 0x2f, 0x40, 0x0,
    0xc9, 0x16, 0xe0, 0x0, 0xc, 0x90, 0x0, 0x2f,
    0x0, 0xc, 0x50, 0x7, 0xe0, 0x0, 0x4, 0xd0,
    0x0, 0xa7, 0x2, 0xf4, 0x0, 0x0, 0x3f, 0x0,
    0xc, 0x60, 0xc9, 0x0, 0x0, 0x0, 0xd7, 0x4,
    0xf1, 0x7e, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xe4,
    0x2f, 0x41, 0xae, 0xb2, 0x0, 0x0, 0x10, 0xc,
    0x90, 0xc9, 0x28, 0xe0, 0x0, 0x0, 0x6, 0xe1,
    0x3f, 0x0, 0xd, 0x50, 0x0, 0x2, 0xf4, 0x4,
    0xd0, 0x0, 0xa7, 0x0, 0x0, 0xba, 0x0, 0x3e,
    0x0, 0xc, 0x50, 0x0, 0x6e, 0x10, 0x0, 0xd7,
    0x5, 0xe1, 0x0, 0x1f, 0x50, 0x0, 0x2, 0xbf,
    0xc3, 0x0,

    /* U+0026 "&" */
    0x0, 0x4, 0xcf, 0xe9, 0x0, 0x0, 0x0, 0x3,
    0xfb, 0x57, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0x0,
    0xa, 0xd0, 0x0, 0x0, 0x8, 0xf1, 0x0, 0xdb,
    0x0, 0x0, 0x0, 0x2f, 0xb3, 0xce, 0x20, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x20, 0x0, 0x0, 0x0,
    0x4e, 0xef, 0xb0, 0x0, 0x10, 0x0, 0x5f, 0xa0,
    0x6f, 0xb0, 0x1f, 0x50, 0xe, 0xc0, 0x0, 0x6f,
    0xb7, 0xf2, 0x2, 0xf8, 0x0, 0x0, 0x6f, 0xfb,
    0x0, 0xf, 0xd0, 0x0, 0x1, 0xdf, 0xb0, 0x0,
    0x8f, 0xe9, 0x8a, 0xfe, 0x8f, 0xb0, 0x0, 0x5c,
    0xff, 0xd9, 0x10, 0x68, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xda, 0xd9, 0xd9, 0xc8, 0xc8, 0x0,

    /* U+0028 "(" */
    0x0, 0x6f, 0x40, 0xd, 0xc0, 0x4, 0xf6, 0x0,
    0x9f, 0x10, 0xd, 0xd0, 0x0, 0xfb, 0x0, 0x2f,
    0x80, 0x3, 0xf7, 0x0, 0x4f, 0x60, 0x4, 0xf6,
    0x0, 0x3f, 0x70, 0x2, 0xf8, 0x0, 0xf, 0xb0,
    0x0, 0xdd, 0x0, 0x8, 0xf1, 0x0, 0x3f, 0x60,
    0x0, 0xdc, 0x0, 0x6, 0xf4,

    /* U+0029 ")" */
    0x3f, 0x70, 0x0, 0xbe, 0x0, 0x5, 0xf5, 0x0,
    0x1f, 0xa0, 0x0, 0xce, 0x0, 0x9, 0xf1, 0x0,
    0x7f, 0x30, 0x6, 0xf4, 0x0, 0x5f, 0x50, 0x5,
    0xf5, 0x0, 0x6f, 0x40, 0x7, 0xf3, 0x0, 0x9f,
    0x10, 0xc, 0xe0, 0x1, 0xfa, 0x0, 0x5f, 0x50,
    0xb, 0xe0, 0x3, 0xf7, 0x0,

    /* U+002A "*" */
    0x0, 0xe, 0x20, 0x3, 0x60, 0xe2, 0x56, 0x4d,
    0xdf, 0xce, 0x60, 0x1d, 0xff, 0x30, 0x4e, 0xcf,
    0xbf, 0x63, 0x60, 0xe2, 0x45, 0x0, 0xe, 0x20,
    0x0,

    /* U+002B "+" */
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x8, 0xf0, 0x0, 0x1, 0x22, 0x9f,
    0x22, 0x20, 0xcf, 0xff, 0xff, 0xff, 0x44, 0x55,
    0xaf, 0x55, 0x51, 0x0, 0x8, 0xf0, 0x0, 0x0,
    0x0, 0x8f, 0x0, 0x0, 0x0, 0x8, 0xf0, 0x0,
    0x0,

    /* U+002C "," */
    0x1, 0x10, 0xe, 0xf1, 0xf, 0xf2, 0x7, 0xe0,
    0xb, 0x90, 0xe, 0x40,

    /* U+002D "-" */
    0x88, 0x88, 0x7f, 0xff, 0xfe,

    /* U+002E "." */
    0x5, 0x60, 0x1f, 0xf2, 0xc, 0xd0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x57, 0x0, 0x0, 0x0, 0xea,
    0x0, 0x0, 0x3, 0xf5, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x4f, 0x40,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0x4, 0xf4, 0x0, 0x0, 0x9, 0xe0, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x4f, 0x40, 0x0,
    0x0, 0xae, 0x0, 0x0, 0x0, 0xf9, 0x0, 0x0,
    0x5, 0xf3, 0x0, 0x0, 0xa, 0xe0, 0x0, 0x0,
    0xf, 0x90, 0x0, 0x0, 0x5f, 0x30, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x4, 0xbe, 0xeb, 0x40, 0x0, 0x0, 0x6f,
    0xfb, 0xbf, 0xf6, 0x0, 0x2, 0xfd, 0x10, 0x1,
    0xdf, 0x20, 0xa, 0xf3, 0x0, 0x0, 0x3f, 0xa0,
    0xe, 0xe0, 0x0, 0x0, 0xe, 0xe0, 0xf, 0xb0,
    0x0, 0x0, 0xb, 0xf0, 0x1f, 0xa0, 0x0, 0x0,
    0xa, 0xf1, 0xf, 0xb0, 0x0, 0x0, 0xb, 0xf0,
    0xe, 0xe0, 0x0, 0x0, 0xe, 0xe0, 0x9, 0xf3,
    0x0, 0x0, 0x3f, 0x90, 0x2, 0xfd, 0x10, 0x1,
    0xdf, 0x20, 0x0, 0x6f, 0xfb, 0xbf, 0xf6, 0x0,
    0x0, 0x4, 0xbe, 0xeb, 0x40, 0x0,

    /* U+0031 "1" */
    0xdf, 0xff, 0xc8, 0xaa, 0xfc, 0x0, 0xf, 0xc0,
    0x0, 0xfc, 0x0, 0xf, 0xc0, 0x0, 0xfc, 0x0,
    0xf, 0xc0, 0x0, 0xfc, 0x0, 0xf, 0xc0, 0x0,
    0xfc, 0x0, 0xf, 0xc0, 0x0, 0xfc, 0x0, 0xf,
    0xc0,

    /* U+0032 "2" */
    0x1, 0x8d, 0xfe, 0xc5, 0x0, 0x4f, 0xfd, 0xac,
    0xff, 0x70, 0x3c, 0x30, 0x0, 0x2f, 0xf0, 0x0,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0xc,
    0xf1, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0,
    0x2, 0xee, 0x20, 0x0, 0x0, 0x2e, 0xf3, 0x0,
    0x0, 0x2, 0xef, 0x30, 0x0, 0x0, 0x2e, 0xf3,
    0x0, 0x0, 0x2, 0xef, 0x30, 0x0, 0x0, 0x2e,
    0xfc, 0xaa, 0xaa, 0xa7, 0x5f, 0xff, 0xff, 0xff,
    0xfb,

    /* U+0033 "3" */
    0x5f, 0xff, 0xff, 0xff, 0xf0, 0x3a, 0xaa, 0xaa,
    0xcf, 0xc0, 0x0, 0x0, 0x1, 0xee, 0x10, 0x0,
    0x0, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x9f, 0x60,
    0x0, 0x0, 0x4, 0xfe, 0x61, 0x0, 0x0, 0x5,
    0xdf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x3e, 0xf2,
    0x0, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0,
    0x6, 0xf6, 0x69, 0x10, 0x0, 0x1d, 0xf2, 0x9f,
    0xfc, 0xbc, 0xff, 0x80, 0x4, 0xae, 0xfe, 0xb5,
    0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0x0,
    0x1, 0xed, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x0, 0x5, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xc0, 0x3, 0xc5, 0x0, 0x0, 0xde, 0x10, 0x4,
    0xf7, 0x0, 0xb, 0xf4, 0x0, 0x4, 0xf7, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x39, 0x99,
    0x99, 0x9b, 0xfc, 0x98, 0x0, 0x0, 0x0, 0x5,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf7, 0x0,

    /* U+0035 "5" */
    0x2, 0xff, 0xff, 0xff, 0xf0, 0x4, 0xfc, 0xaa,
    0xaa, 0xa0, 0x5, 0xf5, 0x0, 0x0, 0x0, 0x7,
    0xf3, 0x0, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xc7, 0x0, 0x7, 0xaa,
    0xab, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xb, 0xf6,
    0x0, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0, 0x0,
    0x3, 0xfa, 0x3c, 0x20, 0x0, 0xb, 0xf6, 0x6f,
    0xfd, 0xbb, 0xef, 0xb0, 0x3, 0x9d, 0xff, 0xc7,
    0x0,

    /* U+0036 "6" */
    0x0, 0x1, 0x8d, 0xfe, 0xc6, 0x0, 0x4, 0xef,
    0xca, 0xad, 0x90, 0x1, 0xee, 0x40, 0x0, 0x0,
    0x0, 0x8f, 0x40, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xfb, 0x2a, 0xef, 0xd7,
    0x0, 0x1f, 0xdf, 0xd9, 0x9d, 0xfb, 0x1, 0xff,
    0xa0, 0x0, 0xa, 0xf5, 0xf, 0xf2, 0x0, 0x0,
    0x3f, 0x90, 0xbf, 0x20, 0x0, 0x3, 0xf8, 0x4,
    0xfa, 0x0, 0x0, 0xaf, 0x40, 0x9, 0xfd, 0x99,
    0xdf, 0xa0, 0x0, 0x5, 0xcf, 0xfc, 0x60, 0x0,

    /* U+0037 "7" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x7, 0xfb, 0xaa,
    0xaa, 0xaf, 0xe0, 0x7f, 0x40, 0x0, 0x5, 0xf8,
    0x6, 0xf4, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xf3,
    0x0, 0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0x0, 0x0, 0x4f, 0x90,
    0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x2a, 0xef, 0xfc, 0x70, 0x0, 0x3f, 0xfb,
    0x89, 0xdf, 0xb0, 0xa, 0xf5, 0x0, 0x0, 0xbf,
    0x30, 0xbf, 0x10, 0x0, 0x8, 0xf4, 0x4, 0xfc,
    0x42, 0x26, 0xfd, 0x0, 0x6, 0xff, 0xff, 0xfe,
    0x10, 0x5, 0xfe, 0x85, 0x6a, 0xfd, 0x10, 0xee,
    0x10, 0x0, 0x7, 0xf8, 0x2f, 0xa0, 0x0, 0x0,
    0x1f, 0xb2, 0xfb, 0x0, 0x0, 0x2, 0xfb, 0xd,
    0xf4, 0x0, 0x0, 0xaf, 0x70, 0x3f, 0xfb, 0x89,
    0xdf, 0xc0, 0x0, 0x29, 0xdf, 0xfc, 0x60, 0x0,

    /* U+0039 "9" */
    0x0, 0x6c, 0xff, 0xc6, 0x0, 0x0, 0xaf, 0xd9,
    0x8c, 0xfa, 0x0, 0x3f, 0xb0, 0x0, 0x7, 0xf6,
    0x7, 0xf5, 0x0, 0x0, 0xf, 0xd0, 0x6f, 0x60,
    0x0, 0x2, 0xff, 0x11, 0xff, 0x51, 0x4, 0xdf,
    0xf2, 0x4, 0xef, 0xff, 0xfb, 0x9f, 0x30, 0x0,
    0x57, 0x73, 0xa, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x0, 0x0, 0x3e, 0xf2, 0x0, 0x8e, 0xba, 0xcf,
    0xf5, 0x0, 0x5, 0xbe, 0xfd, 0x92, 0x0, 0x0,

    /* U+003A ":" */
    0xc, 0xd0, 0x1f, 0xf2, 0x5, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x60,
    0x1f, 0xf2, 0xc, 0xd0,

    /* U+003B ";" */
    0xc, 0xd0, 0x1f, 0xf2, 0x5, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0xe, 0xf1, 0xf, 0xf2, 0x7, 0xe0, 0xb, 0x90,
    0xe, 0x40,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x5, 0x20, 0x0, 0x1, 0x8e,
    0xf4, 0x0, 0x4b, 0xff, 0x93, 0x6, 0xdf, 0xc6,
    0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x5, 0xcf,
    0xe7, 0x10, 0x0, 0x0, 0x39, 0xff, 0xb4, 0x0,
    0x0, 0x1, 0x6d, 0xf4, 0x0, 0x0, 0x0, 0x4,
    0x20,

    /* U+003D "=" */
    0xcf, 0xff, 0xff, 0xff, 0x46, 0x77, 0x77, 0x77,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0xc, 0xff,
    0xff, 0xff, 0xf4, 0x45, 0x55, 0x55, 0x55, 0x10,

    /* U+003E ">" */
    0x62, 0x0, 0x0, 0x0, 0xc, 0xfb, 0x40, 0x0,
    0x0, 0x6, 0xcf, 0xe7, 0x10, 0x0, 0x0, 0x39,
    0xff, 0xa1, 0x0, 0x0, 0x2, 0xdf, 0x40, 0x0,
    0x4b, 0xff, 0x91, 0x18, 0xef, 0xc6, 0x0, 0xc,
    0xfa, 0x30, 0x0, 0x0, 0x51, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x2, 0x9d, 0xfe, 0xc5, 0x0, 0x4f, 0xfb, 0x9b,
    0xff, 0x80, 0x4c, 0x20, 0x0, 0x2f, 0xf0, 0x0,
    0x0, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0x1, 0xcf, 0x30, 0x0, 0x0,
    0x1d, 0xf4, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x0, 0xac, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0xcd, 0x0,
    0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x2, 0x9d, 0xff, 0xec, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc7, 0x54, 0x58, 0xdf,
    0x60, 0x0, 0x0, 0xc, 0xe4, 0x0, 0x0, 0x0,
    0x6, 0xf9, 0x0, 0x0, 0xae, 0x10, 0x3b, 0xef,
    0xb2, 0xbd, 0x3f, 0x60, 0x4, 0xf4, 0x4, 0xfe,
    0x97, 0xcf, 0xed, 0x6, 0xe0, 0xa, 0xc0, 0xe,
    0xe1, 0x0, 0x8, 0xfd, 0x0, 0xe5, 0xe, 0x60,
    0x4f, 0x60, 0x0, 0x0, 0xfd, 0x0, 0xa9, 0x1f,
    0x40, 0x6f, 0x30, 0x0, 0x0, 0xcd, 0x0, 0x8b,
    0x2f, 0x30, 0x6f, 0x30, 0x0, 0x0, 0xcd, 0x0,
    0x8b, 0x1f, 0x40, 0x3f, 0x60, 0x0, 0x0, 0xfd,
    0x0, 0x99, 0xe, 0x60, 0xe, 0xe1, 0x0, 0x8,
    0xfe, 0x0, 0xe5, 0xa, 0xc0, 0x4, 0xfe, 0x87,
    0xbf, 0xaf, 0x9b, 0xe0, 0x4, 0xf4, 0x0, 0x3b,
    0xff, 0xb3, 0x1b, 0xfc, 0x30, 0x0, 0x9e, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xc7, 0x54, 0x6a, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xdb, 0x50, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf6, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdd, 0xb, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x60, 0x4f, 0x70, 0x0, 0x0, 0x0, 0xb,
    0xe0, 0x0, 0xdd, 0x0, 0x0, 0x0, 0x2, 0xf8,
    0x0, 0x6, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0x10,
    0x0, 0xe, 0xc0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x7, 0xf8, 0x88, 0x88,
    0x88, 0xfa, 0x0, 0x0, 0xed, 0x0, 0x0, 0x0,
    0xb, 0xf1, 0x0, 0x5f, 0x60, 0x0, 0x0, 0x0,
    0x4f, 0x80, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xee, 0x0,

    /* U+0042 "B" */
    0x1f, 0xff, 0xff, 0xfe, 0xb4, 0x0, 0x1f, 0xd8,
    0x88, 0x8a, 0xff, 0x70, 0x1f, 0xb0, 0x0, 0x0,
    0x2f, 0xf0, 0x1f, 0xb0, 0x0, 0x0, 0xc, 0xf0,
    0x1f, 0xb0, 0x0, 0x0, 0x2f, 0xc0, 0x1f, 0xd8,
    0x88, 0x8a, 0xfe, 0x30, 0x1f, 0xff, 0xff, 0xff,
    0xfd, 0x40, 0x1f, 0xb0, 0x0, 0x1, 0x4d, 0xf3,
    0x1f, 0xb0, 0x0, 0x0, 0x4, 0xf9, 0x1f, 0xb0,
    0x0, 0x0, 0x2, 0xfb, 0x1f, 0xb0, 0x0, 0x0,
    0x8, 0xf8, 0x1f, 0xd8, 0x88, 0x89, 0xcf, 0xe1,
    0x1f, 0xff, 0xff, 0xff, 0xd9, 0x10,

    /* U+0043 "C" */
    0x0, 0x0, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x1,
    0xbf, 0xfc, 0xac, 0xff, 0xb0, 0x0, 0xbf, 0xb2,
    0x0, 0x1, 0x9c, 0x0, 0x6f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xb2, 0x0, 0x1, 0xac, 0x10,
    0x1, 0xbf, 0xfc, 0xbc, 0xff, 0xb0, 0x0, 0x0,
    0x4b, 0xef, 0xeb, 0x50, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xff, 0xfd, 0xa4, 0x0, 0x1, 0xfe,
    0xaa, 0xaa, 0xcf, 0xfa, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x2b, 0xfa, 0x1, 0xfb, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x3f,
    0xb1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xee, 0x1f,
    0xb0, 0x0, 0x0, 0x0, 0xd, 0xf1, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xee, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0x3f, 0xb1, 0xfb, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x1f, 0xb0, 0x0, 0x0, 0x2b, 0xfa, 0x1,
    0xfe, 0xaa, 0xaa, 0xcf, 0xfa, 0x0, 0x1f, 0xff,
    0xff, 0xfd, 0xa4, 0x0, 0x0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xff, 0xff, 0xfc, 0x1, 0xfe, 0xaa,
    0xaa, 0xaa, 0x70, 0x1f, 0xb0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xb0,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x99, 0x99, 0x99,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf0, 0x1, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xb0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0xaa, 0xaa,
    0xaa, 0xa0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0x0,

    /* U+0046 "F" */
    0x1f, 0xff, 0xff, 0xff, 0xfc, 0x1f, 0xea, 0xaa,
    0xaa, 0xa7, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f,
    0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xf0, 0x1f, 0xe9, 0x99, 0x99, 0x90,
    0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f,
    0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x4a, 0xdf, 0xeb, 0x60, 0x0, 0x1,
    0xbf, 0xfc, 0xbb, 0xff, 0xc1, 0x0, 0xbf, 0xb2,
    0x0, 0x0, 0x7d, 0x10, 0x6f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x39, 0x30, 0xfc, 0x0,
    0x0, 0x0, 0x6, 0xf5, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0x6f, 0x50, 0x6f, 0xb0, 0x0, 0x0, 0x6,
    0xf5, 0x0, 0xbf, 0xb2, 0x0, 0x0, 0x9f, 0x50,
    0x0, 0xaf, 0xfd, 0xbc, 0xff, 0xd2, 0x0, 0x0,
    0x4a, 0xef, 0xeb, 0x60, 0x0,

    /* U+0048 "H" */
    0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb, 0x1f, 0xb0,
    0x0, 0x0, 0x1, 0xfb, 0x1f, 0xb0, 0x0, 0x0,
    0x1, 0xfb, 0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb,
    0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb, 0x1f, 0xea,
    0xaa, 0xaa, 0xaa, 0xfb, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb,
    0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb, 0x1f, 0xb0,
    0x0, 0x0, 0x1, 0xfb, 0x1f, 0xb0, 0x0, 0x0,
    0x1, 0xfb, 0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb,
    0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb,

    /* U+0049 "I" */
    0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1,
    0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f,
    0xb1, 0xfb, 0x1f, 0xb0,

    /* U+004A "J" */
    0x0, 0xef, 0xff, 0xff, 0x70, 0x8, 0xaa, 0xab,
    0xf7, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x5, 0xf7, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0,
    0x0, 0x5, 0xf7, 0x0, 0x0, 0x0, 0x5f, 0x70,
    0x0, 0x0, 0x5, 0xf7, 0x0, 0x0, 0x0, 0x5f,
    0x70, 0x0, 0x0, 0x6, 0xf6, 0xa, 0x70, 0x0,
    0xcf, 0x30, 0xcf, 0xda, 0xdf, 0xc0, 0x0, 0x8d,
    0xfe, 0x90, 0x0,

    /* U+004B "K" */
    0x1f, 0xb0, 0x0, 0x0, 0x1d, 0xe2, 0x1f, 0xb0,
    0x0, 0x1, 0xdf, 0x30, 0x1f, 0xb0, 0x0, 0xc,
    0xf4, 0x0, 0x1f, 0xb0, 0x0, 0xbf, 0x50, 0x0,
    0x1f, 0xb0, 0xb, 0xf7, 0x0, 0x0, 0x1f, 0xb0,
    0xaf, 0x80, 0x0, 0x0, 0x1f, 0xb9, 0xff, 0xb0,
    0x0, 0x0, 0x1f, 0xff, 0xbb, 0xf8, 0x0, 0x0,
    0x1f, 0xfb, 0x1, 0xdf, 0x50, 0x0, 0x1f, 0xd0,
    0x0, 0x2f, 0xf2, 0x0, 0x1f, 0xb0, 0x0, 0x4,
    0xfd, 0x10, 0x1f, 0xb0, 0x0, 0x0, 0x7f, 0xb0,
    0x1f, 0xb0, 0x0, 0x0, 0x9, 0xf8,

    /* U+004C "L" */
    0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f,
    0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0,
    0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x1f,
    0xea, 0xaa, 0xaa, 0xa5, 0x1f, 0xff, 0xff, 0xff,
    0xf8,

    /* U+004D "M" */
    0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x41,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x1f, 0xf4, 0x1f,
    0xfc, 0x0, 0x0, 0x0, 0x9, 0xff, 0x41, 0xff,
    0xf5, 0x0, 0x0, 0x2, 0xfe, 0xf4, 0x1f, 0xad,
    0xe0, 0x0, 0x0, 0xbe, 0x8f, 0x41, 0xfa, 0x4f,
    0x80, 0x0, 0x4f, 0x67, 0xf4, 0x1f, 0xa0, 0xaf,
    0x20, 0xd, 0xd0, 0x7f, 0x41, 0xfa, 0x2, 0xfa,
    0x7, 0xf4, 0x7, 0xf4, 0x1f, 0xa0, 0x8, 0xf5,
    0xea, 0x0, 0x7f, 0x41, 0xfa, 0x0, 0xe, 0xff,
    0x20, 0x6, 0xf4, 0x1f, 0xa0, 0x0, 0x5f, 0x80,
    0x0, 0x6f, 0x41, 0xfa, 0x0, 0x0, 0x60, 0x0,
    0x6, 0xf4, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x40,

    /* U+004E "N" */
    0x1f, 0xb0, 0x0, 0x0, 0x1, 0xfb, 0x1f, 0xf8,
    0x0, 0x0, 0x1, 0xfb, 0x1f, 0xff, 0x50, 0x0,
    0x1, 0xfb, 0x1f, 0xdf, 0xf2, 0x0, 0x1, 0xfb,
    0x1f, 0xb6, 0xfd, 0x0, 0x1, 0xfb, 0x1f, 0xb0,
    0x9f, 0xa0, 0x1, 0xfb, 0x1f, 0xb0, 0xc, 0xf6,
    0x1, 0xfb, 0x1f, 0xb0, 0x1, 0xef, 0x31, 0xfb,
    0x1f, 0xb0, 0x0, 0x4f, 0xe2, 0xfb, 0x1f, 0xb0,
    0x0, 0x7, 0xfd, 0xfb, 0x1f, 0xb0, 0x0, 0x0,
    0xbf, 0xfb, 0x1f, 0xb0, 0x0, 0x0, 0x1d, 0xfb,
    0x1f, 0xb0, 0x0, 0x0, 0x2, 0xfb,

    /* U+004F "O" */
    0x0, 0x0, 0x4a, 0xdf, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0xaf, 0xfc, 0xbc, 0xff, 0xc1, 0x0, 0x0,
    0xbf, 0xb2, 0x0, 0x1, 0xaf, 0xd0, 0x0, 0x6f,
    0xb0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0xc, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf2, 0x1f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x30, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf2, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x6f, 0xb0, 0x0, 0x0,
    0x0, 0x9f, 0x80, 0x0, 0xbf, 0xb2, 0x0, 0x1,
    0xaf, 0xd0, 0x0, 0x0, 0xaf, 0xfc, 0xbc, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x4a, 0xef, 0xeb, 0x50,
    0x0, 0x0,

    /* U+0050 "P" */
    0x1f, 0xff, 0xff, 0xfc, 0x70, 0x0, 0x1f, 0xea,
    0xaa, 0xbe, 0xfd, 0x10, 0x1f, 0xb0, 0x0, 0x0,
    0x9f, 0x90, 0x1f, 0xb0, 0x0, 0x0, 0xe, 0xf0,
    0x1f, 0xb0, 0x0, 0x0, 0xc, 0xf0, 0x1f, 0xb0,
    0x0, 0x0, 0xe, 0xf0, 0x1f, 0xb0, 0x0, 0x0,
    0x9f, 0x90, 0x1f, 0xea, 0xaa, 0xbe, 0xfd, 0x10,
    0x1f, 0xff, 0xff, 0xfc, 0x70, 0x0, 0x1f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x4a, 0xdf, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0xaf, 0xfc, 0xbc, 0xff, 0xc1, 0x0, 0x0,
    0xbf, 0xb2, 0x0, 0x1, 0xaf, 0xd0, 0x0, 0x6f,
    0xb0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0xc, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x1f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x30, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf2, 0xd, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x7f, 0xa0, 0x0, 0x0,
    0x0, 0x8f, 0x80, 0x0, 0xcf, 0xa1, 0x0, 0x0,
    0x9f, 0xd0, 0x0, 0x1, 0xcf, 0xfb, 0xab, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x6c, 0xef, 0xfd, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe6, 0x34,
    0xa7, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2, 0x43, 0x0,

    /* U+0052 "R" */
    0x1f, 0xff, 0xff, 0xfc, 0x70, 0x0, 0x1f, 0xea,
    0xaa, 0xbe, 0xfd, 0x10, 0x1f, 0xb0, 0x0, 0x0,
    0x9f, 0x90, 0x1f, 0xb0, 0x0, 0x0, 0xe, 0xf0,
    0x1f, 0xb0, 0x0, 0x0, 0xc, 0xf0, 0x1f, 0xb0,
    0x0, 0x0, 0xe, 0xe0, 0x1f, 0xb0, 0x0, 0x0,
    0x9f, 0x90, 0x1f, 0xd9, 0x99, 0xae, 0xfd, 0x10,
    0x1f, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x1f, 0xb0,
    0x0, 0x1f, 0xd0, 0x0, 0x1f, 0xb0, 0x0, 0x6,
    0xf9, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0xbf, 0x40,
    0x1f, 0xb0, 0x0, 0x0, 0x1e, 0xe0,

    /* U+0053 "S" */
    0x0, 0x29, 0xdf, 0xfd, 0x81, 0x0, 0x3f, 0xfc,
    0x9a, 0xdf, 0xc0, 0xb, 0xf4, 0x0, 0x0, 0x23,
    0x0, 0xed, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0x61, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xfd, 0x70, 0x0, 0x0,
    0x0, 0x48, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x0, 0x0, 0x6, 0xf6, 0xb,
    0x50, 0x0, 0x0, 0xcf, 0x31, 0xdf, 0xeb, 0x9a,
    0xef, 0x90, 0x0, 0x6b, 0xef, 0xeb, 0x50, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0x89, 0xaa, 0xad,
    0xfb, 0xaa, 0xa5, 0x0, 0x0, 0x9f, 0x20, 0x0,
    0x0, 0x0, 0x9, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x20, 0x0, 0x0, 0x0,
    0x9, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x20,
    0x0, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x20, 0x0, 0x0, 0x0, 0x9, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x20, 0x0, 0x0,

    /* U+0055 "U" */
    0x3f, 0x90, 0x0, 0x0, 0x5, 0xf7, 0x3f, 0x90,
    0x0, 0x0, 0x5, 0xf7, 0x3f, 0x90, 0x0, 0x0,
    0x5, 0xf7, 0x3f, 0x90, 0x0, 0x0, 0x5, 0xf7,
    0x3f, 0x90, 0x0, 0x0, 0x5, 0xf7, 0x3f, 0x90,
    0x0, 0x0, 0x5, 0xf7, 0x3f, 0x90, 0x0, 0x0,
    0x5, 0xf7, 0x3f, 0x90, 0x0, 0x0, 0x5, 0xf6,
    0x2f, 0xb0, 0x0, 0x0, 0x7, 0xf5, 0xe, 0xf0,
    0x0, 0x0, 0xb, 0xf2, 0x8, 0xfa, 0x0, 0x0,
    0x7f, 0xb0, 0x0, 0xcf, 0xfb, 0xbe, 0xfe, 0x20,
    0x0, 0x7, 0xcf, 0xfd, 0x81, 0x0,

    /* U+0056 "V" */
    0xc, 0xf2, 0x0, 0x0, 0x0, 0x2, 0xf9, 0x6,
    0xf8, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0xef,
    0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0x8f, 0x60,
    0x0, 0x0, 0x7f, 0x50, 0x0, 0x1f, 0xd0, 0x0,
    0x0, 0xee, 0x0, 0x0, 0xa, 0xf4, 0x0, 0x5,
    0xf7, 0x0, 0x0, 0x3, 0xfb, 0x0, 0xc, 0xf1,
    0x0, 0x0, 0x0, 0xdf, 0x20, 0x2f, 0xa0, 0x0,
    0x0, 0x0, 0x6f, 0x80, 0x9f, 0x30, 0x0, 0x0,
    0x0, 0xe, 0xe1, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x80, 0x0, 0x0,

    /* U+0057 "W" */
    0x4f, 0x90, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x0,
    0x2, 0xf8, 0xe, 0xe0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x8, 0xf3, 0x9, 0xf3, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x0, 0xd, 0xd0, 0x4, 0xf8,
    0x0, 0x0, 0xcd, 0x8f, 0x20, 0x0, 0x2f, 0x80,
    0x0, 0xfe, 0x0, 0x2, 0xf8, 0x3f, 0x80, 0x0,
    0x7f, 0x30, 0x0, 0xaf, 0x30, 0x7, 0xf3, 0xe,
    0xd0, 0x0, 0xde, 0x0, 0x0, 0x5f, 0x80, 0xc,
    0xd0, 0x8, 0xf2, 0x2, 0xf9, 0x0, 0x0, 0xf,
    0xd0, 0x2f, 0x80, 0x3, 0xf7, 0x7, 0xf4, 0x0,
    0x0, 0xa, 0xf2, 0x7f, 0x30, 0x0, 0xed, 0xc,
    0xe0, 0x0, 0x0, 0x5, 0xf7, 0xdd, 0x0, 0x0,
    0x9f, 0x4f, 0x90, 0x0, 0x0, 0x0, 0xfe, 0xf8,
    0x0, 0x0, 0x3f, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0xe, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x9, 0xfa,
    0x0, 0x0,

    /* U+0058 "X" */
    0x2f, 0xd0, 0x0, 0x0, 0xb, 0xf3, 0x6, 0xfa,
    0x0, 0x0, 0x7f, 0x70, 0x0, 0xbf, 0x50, 0x2,
    0xfc, 0x0, 0x0, 0x1e, 0xe1, 0xd, 0xf2, 0x0,
    0x0, 0x5, 0xfb, 0x8f, 0x50, 0x0, 0x0, 0x0,
    0x9f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfe, 0x10, 0x0,
    0x0, 0x9, 0xf6, 0x5f, 0xa0, 0x0, 0x0, 0x4f,
    0xb0, 0xa, 0xf6, 0x0, 0x1, 0xee, 0x10, 0x1,
    0xef, 0x20, 0xb, 0xf5, 0x0, 0x0, 0x4f, 0xc0,
    0x6f, 0xa0, 0x0, 0x0, 0x8, 0xf8,

    /* U+0059 "Y" */
    0xc, 0xf2, 0x0, 0x0, 0x0, 0x5f, 0x60, 0x3f,
    0xb0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x9f, 0x50,
    0x0, 0x8, 0xf3, 0x0, 0x1, 0xee, 0x0, 0x2,
    0xfa, 0x0, 0x0, 0x6, 0xf8, 0x0, 0xbf, 0x10,
    0x0, 0x0, 0xc, 0xf1, 0x4f, 0x70, 0x0, 0x0,
    0x0, 0x3f, 0xbd, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xa, 0xaa,
    0xaa, 0xaa, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x20, 0x0, 0x0, 0x0, 0xc, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0x5, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0xa, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xa0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xfb, 0xaa, 0xaa, 0xaa, 0xa2,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+005B "[" */
    0x1f, 0xff, 0xa1, 0xfc, 0x74, 0x1f, 0xa0, 0x1,
    0xfa, 0x0, 0x1f, 0xa0, 0x1, 0xfa, 0x0, 0x1f,
    0xa0, 0x1, 0xfa, 0x0, 0x1f, 0xa0, 0x1, 0xfa,
    0x0, 0x1f, 0xa0, 0x1, 0xfa, 0x0, 0x1f, 0xa0,
    0x1, 0xfa, 0x0, 0x1f, 0xa0, 0x1, 0xfa, 0x0,
    0x1f, 0xc7, 0x41, 0xff, 0xfa,

    /* U+005C "\\" */
    0x47, 0x0, 0x0, 0x0, 0x5f, 0x30, 0x0, 0x0,
    0xf, 0x80, 0x0, 0x0, 0xa, 0xe0, 0x0, 0x0,
    0x5, 0xf3, 0x0, 0x0, 0x0, 0xf9, 0x0, 0x0,
    0x0, 0xae, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0xf, 0x90, 0x0, 0x0, 0xa, 0xe0, 0x0,
    0x0, 0x4, 0xf4, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x4f, 0x40,
    0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0xea,

    /* U+005D "]" */
    0xaf, 0xff, 0x14, 0x7c, 0xf1, 0x0, 0xaf, 0x10,
    0xa, 0xf1, 0x0, 0xaf, 0x10, 0xa, 0xf1, 0x0,
    0xaf, 0x10, 0xa, 0xf1, 0x0, 0xaf, 0x10, 0xa,
    0xf1, 0x0, 0xaf, 0x10, 0xa, 0xf1, 0x0, 0xaf,
    0x10, 0xa, 0xf1, 0x0, 0xaf, 0x10, 0xa, 0xf1,
    0x47, 0xcf, 0x1a, 0xff, 0xf1,

    /* U+005E "^" */
    0x0, 0xa, 0xf2, 0x0, 0x0, 0x1, 0xfe, 0x90,
    0x0, 0x0, 0x7d, 0x5e, 0x0, 0x0, 0xd, 0x70,
    0xf5, 0x0, 0x4, 0xf1, 0x9, 0xc0, 0x0, 0xab,
    0x0, 0x3f, 0x20, 0x1f, 0x50, 0x0, 0xd8, 0x7,
    0xe0, 0x0, 0x6, 0xe0,

    /* U+005F "_" */
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x22, 0x22, 0x22,
    0x22,

    /* U+0060 "`" */
    0x48, 0x40, 0x0, 0xaf, 0x40, 0x0, 0x7f, 0x40,

    /* U+0061 "a" */
    0x1, 0x7c, 0xff, 0xd6, 0x0, 0x9, 0xfc, 0x99,
    0xef, 0x80, 0x1, 0x30, 0x0, 0xd, 0xf0, 0x0,
    0x0, 0x0, 0x9, 0xf2, 0x1, 0x9e, 0xff, 0xff,
    0xf3, 0xa, 0xf8, 0x43, 0x3a, 0xf3, 0xf, 0xb0,
    0x0, 0x8, 0xf3, 0xf, 0xb0, 0x0, 0xe, 0xf3,
    0x9, 0xf9, 0x46, 0xdf, 0xf3, 0x0, 0x8d, 0xfe,
    0x87, 0xf3,

    /* U+0062 "b" */
    0x5f, 0x50, 0x0, 0x0, 0x0, 0x5, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x5, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x56,
    0xdf, 0xea, 0x20, 0x5, 0xfd, 0xfc, 0x9b, 0xff,
    0x30, 0x5f, 0xf5, 0x0, 0x4, 0xfe, 0x5, 0xfa,
    0x0, 0x0, 0x8, 0xf4, 0x5f, 0x60, 0x0, 0x0,
    0x4f, 0x75, 0xf6, 0x0, 0x0, 0x4, 0xf7, 0x5f,
    0xa0, 0x0, 0x0, 0x8f, 0x45, 0xff, 0x50, 0x0,
    0x4f, 0xe0, 0x5f, 0xcf, 0xc9, 0xbf, 0xf3, 0x5,
    0xf4, 0x6d, 0xfe, 0xa2, 0x0,

    /* U+0063 "c" */
    0x0, 0x7, 0xdf, 0xeb, 0x30, 0x0, 0xcf, 0xd9,
    0xaf, 0xf4, 0x9, 0xf7, 0x0, 0x2, 0xc3, 0xf,
    0xd0, 0x0, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x0,
    0x0, 0x3f, 0x80, 0x0, 0x0, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x2, 0xc3,
    0x0, 0xcf, 0xd9, 0xaf, 0xf3, 0x0, 0x7, 0xdf,
    0xeb, 0x30,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0,
    0x0, 0x1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x8,
    0xdf, 0xe8, 0x2f, 0xa0, 0x1d, 0xfd, 0x9b, 0xfd,
    0xfa, 0xa, 0xf8, 0x0, 0x2, 0xef, 0xa0, 0xfd,
    0x0, 0x0, 0x6, 0xfa, 0x3f, 0x80, 0x0, 0x0,
    0x2f, 0xa3, 0xf8, 0x0, 0x0, 0x2, 0xfa, 0xf,
    0xc0, 0x0, 0x0, 0x5f, 0xa0, 0xaf, 0x60, 0x0,
    0x1e, 0xfa, 0x1, 0xdf, 0xb7, 0x9e, 0xdf, 0xa0,
    0x0, 0x8d, 0xfe, 0x91, 0xfa,

    /* U+0065 "e" */
    0x0, 0x8, 0xdf, 0xe9, 0x10, 0x0, 0x1d, 0xfb,
    0x8a, 0xfe, 0x20, 0xa, 0xf3, 0x0, 0x3, 0xfb,
    0x0, 0xfa, 0x0, 0x0, 0x9, 0xf1, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0x33, 0xfa, 0x33, 0x33, 0x33,
    0x30, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x90, 0x0, 0x9, 0x10, 0x0, 0xdf, 0xda, 0xae,
    0xf6, 0x0, 0x0, 0x7d, 0xff, 0xc5, 0x0,

    /* U+0066 "f" */
    0x0, 0x1a, 0xee, 0x90, 0xa, 0xf9, 0x88, 0x0,
    0xfb, 0x0, 0x0, 0x1f, 0x90, 0x0, 0xbf, 0xff,
    0xff, 0x55, 0x8f, 0xc7, 0x72, 0x1, 0xfa, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0x1f, 0xa0, 0x0, 0x1, 0xfa, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x1, 0xfa, 0x0, 0x0, 0x1f, 0xa0,
    0x0,

    /* U+0067 "g" */
    0x0, 0x8, 0xdf, 0xe9, 0x1e, 0xc0, 0x1d, 0xfd,
    0x9b, 0xfe, 0xec, 0xa, 0xf8, 0x0, 0x1, 0xdf,
    0xc0, 0xfd, 0x0, 0x0, 0x4, 0xfc, 0x3f, 0x90,
    0x0, 0x0, 0xf, 0xc3, 0xf8, 0x0, 0x0, 0x0,
    0xfc, 0xf, 0xd0, 0x0, 0x0, 0x4f, 0xc0, 0xaf,
    0x80, 0x0, 0x1d, 0xfc, 0x1, 0xdf, 0xd9, 0xaf,
    0xdf, 0xc0, 0x0, 0x8d, 0xfe, 0x91, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x90, 0x27, 0x0, 0x0,
    0xb, 0xf4, 0x7, 0xff, 0xb9, 0xae, 0xfa, 0x0,
    0x4, 0xae, 0xff, 0xc6, 0x0,

    /* U+0068 "h" */
    0x5f, 0x50, 0x0, 0x0, 0x0, 0x5f, 0x50, 0x0,
    0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0, 0x5f,
    0x50, 0x0, 0x0, 0x0, 0x5f, 0x56, 0xdf, 0xea,
    0x10, 0x5f, 0xef, 0xca, 0xdf, 0xd0, 0x5f, 0xf4,
    0x0, 0xa, 0xf6, 0x5f, 0x90, 0x0, 0x2, 0xf9,
    0x5f, 0x60, 0x0, 0x0, 0xfa, 0x5f, 0x50, 0x0,
    0x0, 0xfb, 0x5f, 0x50, 0x0, 0x0, 0xfb, 0x5f,
    0x50, 0x0, 0x0, 0xfb, 0x5f, 0x50, 0x0, 0x0,
    0xfb, 0x5f, 0x50, 0x0, 0x0, 0xfb,

    /* U+0069 "i" */
    0x6f, 0x69, 0xf9, 0x4, 0x0, 0x0, 0x5f, 0x55,
    0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f,
    0x55, 0xf5, 0x5f, 0x55, 0xf5,

    /* U+006A "j" */
    0x0, 0x4, 0xf7, 0x0, 0x7, 0xfa, 0x0, 0x0,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf7, 0x0,
    0x4, 0xf7, 0x0, 0x4, 0xf7, 0x0, 0x4, 0xf7,
    0x0, 0x4, 0xf7, 0x0, 0x4, 0xf7, 0x0, 0x4,
    0xf7, 0x0, 0x4, 0xf7, 0x0, 0x4, 0xf7, 0x0,
    0x4, 0xf7, 0x0, 0x4, 0xf7, 0x0, 0x6, 0xf5,
    0x4b, 0x9f, 0xe1, 0x5e, 0xfc, 0x30,

    /* U+006B "k" */
    0x5f, 0x50, 0x0, 0x0, 0x0, 0x5, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0,
    0x5, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x50,
    0x0, 0x1d, 0xf3, 0x5, 0xf5, 0x0, 0x2d, 0xf3,
    0x0, 0x5f, 0x50, 0x2e, 0xf4, 0x0, 0x5, 0xf5,
    0x2e, 0xf4, 0x0, 0x0, 0x5f, 0x9e, 0xfe, 0x0,
    0x0, 0x5, 0xff, 0xfa, 0xfa, 0x0, 0x0, 0x5f,
    0xe3, 0xc, 0xf6, 0x0, 0x5, 0xf6, 0x0, 0x1e,
    0xf3, 0x0, 0x5f, 0x50, 0x0, 0x4f, 0xd0, 0x5,
    0xf5, 0x0, 0x0, 0x7f, 0xa0,

    /* U+006C "l" */
    0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55,
    0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f,
    0x55, 0xf5, 0x5f, 0x55, 0xf5,

    /* U+006D "m" */
    0x5f, 0x58, 0xdf, 0xe8, 0x0, 0x8d, 0xfe, 0x80,
    0x5, 0xfe, 0xfa, 0x9d, 0xfb, 0xdf, 0xa9, 0xdf,
    0xb0, 0x5f, 0xf2, 0x0, 0xd, 0xff, 0x30, 0x0,
    0xcf, 0x35, 0xf9, 0x0, 0x0, 0x7f, 0xa0, 0x0,
    0x6, 0xf6, 0x5f, 0x60, 0x0, 0x5, 0xf7, 0x0,
    0x0, 0x4f, 0x75, 0xf5, 0x0, 0x0, 0x5f, 0x60,
    0x0, 0x4, 0xf7, 0x5f, 0x50, 0x0, 0x5, 0xf6,
    0x0, 0x0, 0x4f, 0x75, 0xf5, 0x0, 0x0, 0x5f,
    0x60, 0x0, 0x4, 0xf7, 0x5f, 0x50, 0x0, 0x5,
    0xf6, 0x0, 0x0, 0x4f, 0x75, 0xf5, 0x0, 0x0,
    0x5f, 0x60, 0x0, 0x4, 0xf7,

    /* U+006E "n" */
    0x5f, 0x57, 0xdf, 0xea, 0x10, 0x5f, 0xef, 0xa8,
    0xcf, 0xd0, 0x5f, 0xf3, 0x0, 0x9, 0xf6, 0x5f,
    0x90, 0x0, 0x2, 0xf9, 0x5f, 0x60, 0x0, 0x0,
    0xfa, 0x5f, 0x50, 0x0, 0x0, 0xfb, 0x5f, 0x50,
    0x0, 0x0, 0xfb, 0x5f, 0x50, 0x0, 0x0, 0xfb,
    0x5f, 0x50, 0x0, 0x0, 0xfb, 0x5f, 0x50, 0x0,
    0x0, 0xfb,

    /* U+006F "o" */
    0x0, 0x7, 0xdf, 0xea, 0x30, 0x0, 0xd, 0xfd,
    0x9a, 0xff, 0x50, 0x9, 0xf7, 0x0, 0x2, 0xef,
    0x10, 0xfd, 0x0, 0x0, 0x6, 0xf7, 0x3f, 0x80,
    0x0, 0x0, 0x2f, 0x93, 0xf8, 0x0, 0x0, 0x2,
    0xf9, 0xf, 0xd0, 0x0, 0x0, 0x6f, 0x60, 0x9f,
    0x80, 0x0, 0x2e, 0xf1, 0x0, 0xcf, 0xd9, 0xaf,
    0xf4, 0x0, 0x0, 0x7d, 0xfe, 0xa3, 0x0,

    /* U+0070 "p" */
    0x5f, 0x46, 0xdf, 0xea, 0x20, 0x5, 0xfd, 0xfa,
    0x8a, 0xff, 0x30, 0x5f, 0xf4, 0x0, 0x3, 0xfe,
    0x5, 0xfa, 0x0, 0x0, 0x8, 0xf4, 0x5f, 0x60,
    0x0, 0x0, 0x4f, 0x75, 0xf6, 0x0, 0x0, 0x4,
    0xf7, 0x5f, 0xa0, 0x0, 0x0, 0x9f, 0x45, 0xff,
    0x50, 0x0, 0x4f, 0xe0, 0x5f, 0xdf, 0xc9, 0xbf,
    0xf3, 0x5, 0xf5, 0x6d, 0xfe, 0xa2, 0x0, 0x5f,
    0x50, 0x0, 0x0, 0x0, 0x5, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x50, 0x0, 0x0, 0x0, 0x5,
    0xf5, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x8, 0xdf, 0xe8, 0x1f, 0xa0, 0x1d, 0xfd,
    0x9b, 0xfc, 0xfa, 0xa, 0xf7, 0x0, 0x2, 0xef,
    0xa0, 0xfd, 0x0, 0x0, 0x6, 0xfa, 0x3f, 0x80,
    0x0, 0x0, 0x2f, 0xa3, 0xf8, 0x0, 0x0, 0x2,
    0xfa, 0xf, 0xd0, 0x0, 0x0, 0x6f, 0xa0, 0xaf,
    0x80, 0x0, 0x2e, 0xfa, 0x1, 0xdf, 0xd9, 0xaf,
    0xdf, 0xa0, 0x0, 0x8d, 0xfe, 0x82, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0x0, 0x1, 0xfa,

    /* U+0072 "r" */
    0x5f, 0x46, 0xdb, 0x5f, 0xcf, 0xd9, 0x5f, 0xf5,
    0x0, 0x5f, 0xa0, 0x0, 0x5f, 0x70, 0x0, 0x5f,
    0x50, 0x0, 0x5f, 0x50, 0x0, 0x5f, 0x50, 0x0,
    0x5f, 0x50, 0x0, 0x5f, 0x50, 0x0,

    /* U+0073 "s" */
    0x1, 0x8d, 0xfe, 0xc7, 0x0, 0xcf, 0xb8, 0xad,
    0xd0, 0x3f, 0x90, 0x0, 0x1, 0x2, 0xfb, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xb8, 0x40, 0x0, 0x5,
    0x9c, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x9f, 0x60,
    0x50, 0x0, 0x5, 0xf7, 0x5f, 0xea, 0x9a, 0xfe,
    0x10, 0x6c, 0xef, 0xd9, 0x20,

    /* U+0074 "t" */
    0x1, 0xfa, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0xbf,
    0xff, 0xff, 0x55, 0x8f, 0xc7, 0x72, 0x1, 0xfa,
    0x0, 0x0, 0x1f, 0xa0, 0x0, 0x1, 0xfa, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0xbf, 0xa9, 0x90, 0x1,
    0xbe, 0xe9,

    /* U+0075 "u" */
    0x7f, 0x40, 0x0, 0x3, 0xf8, 0x7f, 0x40, 0x0,
    0x3, 0xf8, 0x7f, 0x40, 0x0, 0x3, 0xf8, 0x7f,
    0x40, 0x0, 0x3, 0xf8, 0x7f, 0x40, 0x0, 0x3,
    0xf8, 0x7f, 0x40, 0x0, 0x4, 0xf8, 0x6f, 0x60,
    0x0, 0x6, 0xf8, 0x2f, 0xc0, 0x0, 0x1e, 0xf8,
    0xa, 0xfd, 0x89, 0xee, 0xf8, 0x0, 0x8d, 0xfe,
    0x92, 0xf8,

    /* U+0076 "v" */
    0xd, 0xe0, 0x0, 0x0, 0xd, 0xd0, 0x6, 0xf6,
    0x0, 0x0, 0x3f, 0x70, 0x0, 0xfc, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x9f, 0x30, 0x1, 0xf9, 0x0,
    0x0, 0x2f, 0x90, 0x7, 0xf3, 0x0, 0x0, 0xb,
    0xf0, 0xe, 0xc0, 0x0, 0x0, 0x5, 0xf6, 0x5f,
    0x50, 0x0, 0x0, 0x0, 0xed, 0xbe, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf1, 0x0, 0x0,

    /* U+0077 "w" */
    0xbe, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0xae,
    0x5, 0xf4, 0x0, 0x2, 0xff, 0x60, 0x0, 0xf,
    0x80, 0xf, 0x90, 0x0, 0x8f, 0xeb, 0x0, 0x5,
    0xf2, 0x0, 0xae, 0x0, 0xd, 0xb8, 0xf1, 0x0,
    0xbd, 0x0, 0x4, 0xf4, 0x3, 0xf5, 0x2f, 0x70,
    0x1f, 0x70, 0x0, 0xe, 0xa0, 0x9e, 0x0, 0xcc,
    0x6, 0xf1, 0x0, 0x0, 0x9f, 0x1e, 0x90, 0x6,
    0xf2, 0xcc, 0x0, 0x0, 0x3, 0xfa, 0xf3, 0x0,
    0x1f, 0xaf, 0x60, 0x0, 0x0, 0xd, 0xfd, 0x0,
    0x0, 0xbf, 0xf1, 0x0, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x5, 0xfb, 0x0, 0x0,

    /* U+0078 "x" */
    0x3f, 0xb0, 0x0, 0xc, 0xf2, 0x7, 0xf7, 0x0,
    0x7f, 0x60, 0x0, 0xbf, 0x33, 0xfa, 0x0, 0x0,
    0x1e, 0xde, 0xd0, 0x0, 0x0, 0x4, 0xff, 0x30,
    0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0, 0x3f,
    0xbc, 0xf2, 0x0, 0x0, 0xde, 0x12, 0xfc, 0x0,
    0xa, 0xf4, 0x0, 0x5f, 0x90, 0x6f, 0x80, 0x0,
    0xa, 0xf5,

    /* U+0079 "y" */
    0xd, 0xe0, 0x0, 0x0, 0xc, 0xd0, 0x6, 0xf6,
    0x0, 0x0, 0x3f, 0x70, 0x0, 0xfc, 0x0, 0x0,
    0x9f, 0x10, 0x0, 0x9f, 0x30, 0x1, 0xfa, 0x0,
    0x0, 0x2f, 0x90, 0x6, 0xf3, 0x0, 0x0, 0xc,
    0xf0, 0xd, 0xc0, 0x0, 0x0, 0x5, 0xf6, 0x3f,
    0x60, 0x0, 0x0, 0x0, 0xed, 0xae, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xf, 0xb0,
    0x0, 0x0, 0x1, 0x0, 0x7f, 0x40, 0x0, 0x0,
    0x1f, 0xaa, 0xfb, 0x0, 0x0, 0x0, 0x19, 0xee,
    0xa1, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x2f, 0xff, 0xff, 0xff, 0x91, 0x77, 0x77, 0x7e,
    0xf4, 0x0, 0x0, 0x6, 0xf8, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x1, 0xee, 0x10, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0x4f, 0xb0, 0x0, 0x0, 0x1e, 0xf8, 0x77, 0x77,
    0x54, 0xff, 0xff, 0xff, 0xfb,

    /* U+007B "{" */
    0x0, 0x7e, 0xf0, 0x3f, 0xd7, 0x7, 0xf4, 0x0,
    0x8f, 0x30, 0x8, 0xf3, 0x0, 0x8f, 0x30, 0x8,
    0xf3, 0x0, 0x9f, 0x20, 0x8e, 0xe0, 0xf, 0xfa,
    0x0, 0xb, 0xf2, 0x0, 0x8f, 0x30, 0x8, 0xf3,
    0x0, 0x8f, 0x30, 0x8, 0xf3, 0x0, 0x7f, 0x40,
    0x4, 0xfd, 0x70, 0x8, 0xef,

    /* U+007C "|" */
    0x1f, 0x71, 0xf7, 0x1f, 0x71, 0xf7, 0x1f, 0x71,
    0xf7, 0x1f, 0x71, 0xf7, 0x1f, 0x71, 0xf7, 0x1f,
    0x71, 0xf7, 0x1f, 0x71, 0xf7, 0x1f, 0x71, 0xf7,
    0x1f, 0x71, 0xf7,

    /* U+007D "}" */
    0xaf, 0xa1, 0x0, 0x4b, 0xf9, 0x0, 0x0, 0xed,
    0x0, 0x0, 0xde, 0x0, 0x0, 0xde, 0x0, 0x0,
    0xde, 0x0, 0x0, 0xde, 0x0, 0x0, 0xde, 0x0,
    0x0, 0xaf, 0x92, 0x0, 0x5f, 0xf4, 0x0, 0xcf,
    0x10, 0x0, 0xde, 0x0, 0x0, 0xde, 0x0, 0x0,
    0xde, 0x0, 0x0, 0xde, 0x0, 0x0, 0xed, 0x0,
    0x4a, 0xf9, 0x0, 0xaf, 0xb1, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x1, 0x1, 0xdf, 0xc1, 0x0,
    0xc5, 0xac, 0x5c, 0xe3, 0x4f, 0x2d, 0x40, 0x9,
    0xff, 0x90, 0x30, 0x0, 0x2, 0x10, 0x0,

    /* U+00B0 "°" */
    0x1, 0xaf, 0xd5, 0x0, 0xb8, 0x3, 0xe4, 0x1e,
    0x0, 0x6, 0xa2, 0xe0, 0x0, 0x6a, 0xc, 0x70,
    0x1d, 0x50, 0x2c, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0,

    /* U+2022 "•" */
    0x5, 0x30, 0x9f, 0xf3, 0xcf, 0xf6, 0x4e, 0xc1,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xba, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0xdf, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x1, 0x6a,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x7c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xb7, 0x3f,
    0xf2, 0x0, 0x0, 0x5f, 0xff, 0xfe, 0x95, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x5, 0xff, 0x73, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0,
    0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x20, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0x69,
    0x8f, 0xf2, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0x20, 0x3, 0x58, 0xfe, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf2, 0x2d, 0xff, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0xfd, 0xb, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x4, 0xbd, 0xc8, 0x10,
    0xaf, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x51, 0x6, 0x88, 0x88, 0x88, 0x88, 0x88, 0x30,
    0x15, 0xf7, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0x8f, 0xfd, 0xcf, 0xf3, 0x33, 0x33, 0x33,
    0x6f, 0xec, 0xdf, 0xf2, 0xc, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0x70, 0x2f, 0xf2, 0xc, 0xe0, 0x0,
    0x0, 0x0, 0x3f, 0x80, 0x2f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf4, 0x2d,
    0xf9, 0x99, 0x99, 0x99, 0xbf, 0x92, 0x4f, 0xf2,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x2f,
    0xfb, 0xaf, 0xf1, 0x11, 0x11, 0x11, 0x5f, 0xda,
    0xbf, 0xf9, 0x8e, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0xc8, 0x9f, 0xf2, 0xc, 0xe0, 0x0, 0x0, 0x0,
    0x3f, 0x70, 0x2f, 0xf6, 0x4d, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0xa4, 0x6f, 0xfe, 0xef, 0xfb, 0xbb,
    0xbb, 0xbb, 0xcf, 0xfe, 0xef, 0xc2, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x2c,

    /* U+F00B "" */
    0x58, 0x88, 0x70, 0x28, 0x88, 0x88, 0x88, 0x88,
    0x85, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xd1, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xe1, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x58, 0x88, 0x60, 0x27, 0x88, 0x88,
    0x88, 0x88, 0x85, 0x47, 0x77, 0x50, 0x17, 0x77,
    0x77, 0x77, 0x77, 0x74, 0xff, 0xff, 0xf3, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xf1, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x90, 0x9, 0xd2, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xf9, 0x0, 0x9f, 0xfe,
    0x20, 0x0, 0x8, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0xff, 0xe2, 0x0, 0x8f, 0xff, 0xf9, 0x0, 0x0,
    0x2e, 0xff, 0xfe, 0x28, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x2d, 0xb0, 0x0, 0x0, 0x6, 0xe6, 0xd, 0xff,
    0xc0, 0x0, 0x6, 0xff, 0xf3, 0xcf, 0xff, 0xc0,
    0x6, 0xff, 0xff, 0x31, 0xdf, 0xff, 0xc7, 0xff,
    0xff, 0x50, 0x1, 0xdf, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x6, 0xff, 0xff, 0xdf,
    0xff, 0xc0, 0x6, 0xff, 0xff, 0x51, 0xdf, 0xff,
    0xc0, 0xff, 0xff, 0x50, 0x1, 0xdf, 0xff, 0x58,
    0xff, 0x50, 0x0, 0x1, 0xdf, 0xd0, 0x5, 0x30,
    0x0, 0x0, 0x1, 0x61, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0xcd, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xe3, 0x3, 0xff,
    0xa0, 0xb, 0xc1, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0x3f, 0xfa, 0x4, 0xff, 0xd1, 0x0, 0x4, 0xff,
    0xf6, 0x3, 0xff, 0xa0, 0x1e, 0xff, 0xa0, 0x0,
    0xdf, 0xf7, 0x0, 0x3f, 0xfa, 0x0, 0x2e, 0xff,
    0x40, 0x3f, 0xfc, 0x0, 0x3, 0xff, 0xa0, 0x0,
    0x6f, 0xfa, 0x8, 0xff, 0x60, 0x0, 0x3f, 0xfa,
    0x0, 0x0, 0xef, 0xf0, 0xaf, 0xf2, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0xb, 0xff, 0x1b, 0xff, 0x10,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xbf, 0xf1, 0x9f,
    0xf3, 0x0, 0x0, 0x24, 0x0, 0x0, 0xd, 0xff,
    0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf8, 0x0, 0x9f, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x9f, 0xfe, 0x10, 0x0, 0xcf, 0xfe,
    0x71, 0x0, 0x4, 0xcf, 0xff, 0x50, 0x0, 0x1,
    0xdf, 0xff, 0xfe, 0xdf, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xce, 0xfd, 0xa5,
    0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x4, 0x66, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x78, 0x17, 0xff, 0xff,
    0xff, 0x71, 0x87, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x3f, 0xff,
    0xff, 0xfe, 0x88, 0xef, 0xff, 0xff, 0xf3, 0x8,
    0xff, 0xff, 0xd0, 0x0, 0xd, 0xff, 0xff, 0x80,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x5, 0xff, 0xfa,
    0x0, 0x0, 0xbf, 0xff, 0x30, 0x0, 0x3, 0xff,
    0xfb, 0x0, 0x1, 0xcf, 0xff, 0x80, 0x0, 0x8,
    0xff, 0xfc, 0x10, 0x3e, 0xff, 0xff, 0xf6, 0x0,
    0x6f, 0xff, 0xff, 0xe3, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf,
    0x8e, 0xff, 0xff, 0xff, 0xe8, 0xfd, 0x0, 0x0,
    0x11, 0x1, 0x9f, 0xff, 0xf9, 0x10, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xee, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0x92, 0x0, 0x6b,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xe4, 0xa, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xf6, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0x31, 0xcf, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x25, 0x70,
    0xaf, 0xff, 0xf2, 0x0, 0x0, 0x1, 0xbf, 0xfa,
    0x8, 0xff, 0xb0, 0x7f, 0xff, 0x40, 0x0, 0x2,
    0xdf, 0xf8, 0xa, 0xff, 0xff, 0xd2, 0x5f, 0xff,
    0x50, 0x4, 0xff, 0xf5, 0x1d, 0xff, 0xff, 0xff,
    0xe4, 0x2e, 0xff, 0x70, 0xdf, 0xe3, 0x3e, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x1c, 0xff, 0x13, 0xb1,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x9,
    0x60, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd,
    0x88, 0xbf, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x70, 0x3, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf7, 0x0, 0x3f, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x70,
    0x3, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf5, 0x0, 0x2f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x9, 0xaa, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xee, 0xef, 0xff, 0xfe, 0xee, 0xc0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x24, 0x44, 0x44, 0x7,
    0xff, 0x70, 0x44, 0x44, 0x42, 0xff, 0xff, 0xff,
    0xc1, 0x66, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x66, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x66, 0xc4,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x0, 0x4, 0x44, 0x44, 0x44, 0x44, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x6, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x50, 0x0, 0xbf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x10, 0x6f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xfa, 0xe, 0xff, 0xcc, 0xcc, 0x20, 0x0, 0x0,
    0xbc, 0xcc, 0xef, 0xf2, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xf8, 0x88, 0x8e, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x15, 0x66, 0x40, 0x0, 0x5,
    0xcb, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0x92,
    0x7, 0xff, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x66, 0xff, 0x0, 0x8f, 0xff, 0xa4, 0x12,
    0x5b, 0xff, 0xfd, 0xff, 0x4, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xd, 0xff, 0x30,
    0x0, 0x0, 0x45, 0x46, 0xff, 0xff, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0x8f,
    0xf1, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0x2, 0x10, 0x0, 0x0, 0x0, 0x12, 0x22, 0x22,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x67, 0x77, 0x77, 0x75, 0x0, 0x0,
    0x0, 0x6, 0x73, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x3f, 0xf6, 0xff, 0xff, 0xee, 0xfd,
    0x0, 0x0, 0x0, 0xcf, 0xf1, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xa0, 0xff, 0xff,
    0xfb, 0x20, 0x0, 0x2, 0xbf, 0xfe, 0x10, 0xff,
    0x8d, 0xff, 0xfc, 0xa9, 0xcf, 0xff, 0xe2, 0x0,
    0xff, 0x61, 0x9f, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0xff, 0x70, 0x1, 0x7c, 0xee, 0xd9, 0x30,
    0x0, 0x0, 0x56, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0, 0x3,
    0xef, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xdf,
    0xff, 0xf0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x0,
    0x0, 0xbf, 0xf0, 0x0, 0x0, 0x0, 0xbe, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xf0, 0x7, 0x10, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x3f, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xd, 0xf3, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0x70, 0x8b, 0xbb,
    0xdf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x40, 0x0, 0x0, 0x2d, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0x0, 0x0, 0x40, 0x1c,
    0xf4, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x5f, 0xb0, 0x1e, 0xe1, 0x0, 0x0, 0x3, 0xef,
    0xff, 0x0, 0x0, 0xaf, 0xa0, 0x6f, 0x70, 0xdf,
    0xff, 0xff, 0xff, 0xf0, 0x7, 0x10, 0xbf, 0x30,
    0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0x3, 0xfd,
    0x3, 0xf9, 0xa, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x7, 0xf5, 0xe, 0xc0, 0x8f, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x4f, 0x70, 0xdd, 0x7,
    0xf3, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1d, 0xf3,
    0xf, 0xb0, 0x9f, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x3, 0xf7, 0x7, 0xf6, 0xc, 0xf0, 0x7b, 0xbb,
    0xdf, 0xff, 0xf0, 0x0, 0x3, 0xfe, 0x12, 0xfa,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x3, 0xff,
    0x40, 0xaf, 0x30, 0x0, 0x0, 0x0, 0xbf, 0xf0,
    0x0, 0x3c, 0x30, 0x6f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0x6f, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x90, 0x0, 0x0,

    /* U+F03E "" */
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xa3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xd5, 0x6e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0x50, 0x7,
    0xff, 0xff, 0xf5, 0x8, 0xff, 0xff, 0xff, 0xfb,
    0xbf, 0xff, 0xff, 0x50, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xcb, 0xff, 0xf5, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xfc, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xc0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x74, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x47, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F043 "" */
    0x0, 0x0, 0x6, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0x2c, 0xf8, 0x4f, 0xff,
    0xff, 0xff, 0xf0, 0x7f, 0xf2, 0x5c, 0xff, 0xff,
    0xfb, 0x0, 0xef, 0xe5, 0x8, 0xff, 0xff, 0x30,
    0x2, 0xef, 0xff, 0xff, 0xff, 0x50, 0x0, 0x1,
    0xaf, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x3,
    0x31, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x6b, 0x90, 0x0, 0x0, 0x3, 0xa2, 0x9f, 0xe0,
    0x0, 0x0, 0x4f, 0xf9, 0x9f, 0xe0, 0x0, 0x5,
    0xff, 0xfa, 0x9f, 0xe0, 0x0, 0x6f, 0xff, 0xfa,
    0x9f, 0xe0, 0x7, 0xff, 0xff, 0xfa, 0x9f, 0xe0,
    0x8f, 0xff, 0xff, 0xfa, 0x9f, 0xe9, 0xff, 0xff,
    0xff, 0xfa, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x9f, 0xfe,
    0xff, 0xff, 0xff, 0xfa, 0x9f, 0xe1, 0xdf, 0xff,
    0xff, 0xfa, 0x9f, 0xe0, 0x1c, 0xff, 0xff, 0xfa,
    0x9f, 0xe0, 0x0, 0xbf, 0xff, 0xfa, 0x9f, 0xe0,
    0x0, 0xa, 0xff, 0xfa, 0x9f, 0xe0, 0x0, 0x0,
    0x9f, 0xfa, 0x9f, 0xe0, 0x0, 0x0, 0x8, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x3a, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x3a, 0xbb, 0xb9, 0x10, 0x3, 0xab, 0xbb, 0x91,
    0xef, 0xff, 0xff, 0xa0, 0xe, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xfe, 0x40, 0x7, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x91,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x4a, 0x20, 0x0, 0x0, 0xa, 0xb4, 0xbf, 0xe3,
    0x0, 0x0, 0xf, 0xf8, 0xcf, 0xff, 0x40, 0x0,
    0xf, 0xf8, 0xcf, 0xff, 0xf5, 0x0, 0xf, 0xf8,
    0xcf, 0xff, 0xff, 0x60, 0xf, 0xf8, 0xcf, 0xff,
    0xff, 0xf7, 0xf, 0xf8, 0xcf, 0xff, 0xff, 0xff,
    0x9f, 0xf8, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xff, 0xff, 0xdf, 0xf8, 0xcf, 0xff, 0xff, 0xfc,
    0x1f, 0xf8, 0xcf, 0xff, 0xff, 0xb0, 0xf, 0xf8,
    0xcf, 0xff, 0xfa, 0x0, 0xf, 0xf8, 0xcf, 0xff,
    0x80, 0x0, 0xf, 0xf8, 0xbf, 0xf7, 0x0, 0x0,
    0xf, 0xf8, 0x7f, 0x60, 0x0, 0x0, 0xf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2, 0xca, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x34, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x42, 0x0, 0x3, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x10, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x20, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0,
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff, 0x50,
    0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f,
    0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0x20, 0x0, 0x0, 0x0, 0x3c, 0x60,

    /* U+F054 "" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc1,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x20, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0,
    0x0, 0x9f, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff,
    0x50, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0, 0x0,
    0x9f, 0xff, 0x60, 0x0, 0x0, 0xe, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x3d, 0x50, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x39, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x36, 0x77, 0x77, 0xef, 0xfc, 0x77, 0x77, 0x61,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xae, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x61,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x3, 0x68, 0x87, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xff, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xfd, 0x63, 0x25, 0xbf, 0xff, 0x70, 0x0, 0x0,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xa0, 0x0, 0x6, 0xff, 0xfd, 0x0, 0x8, 0xfc,
    0x20, 0x9f, 0xff, 0xa0, 0x2, 0xff, 0xff, 0x50,
    0x0, 0x8f, 0xfe, 0x12, 0xff, 0xff, 0x60, 0xcf,
    0xff, 0xf2, 0x16, 0x7f, 0xff, 0xf5, 0xe, 0xff,
    0xfe, 0x1e, 0xff, 0xff, 0x12, 0xff, 0xff, 0xff,
    0x70, 0xdf, 0xff, 0xf2, 0x6f, 0xff, 0xf3, 0xe,
    0xff, 0xff, 0xf3, 0xf, 0xff, 0xfb, 0x0, 0xbf,
    0xff, 0x90, 0x4f, 0xff, 0xf8, 0x5, 0xff, 0xfe,
    0x10, 0x1, 0xdf, 0xff, 0x40, 0x28, 0x84, 0x1,
    0xef, 0xff, 0x30, 0x0, 0x1, 0xbf, 0xff, 0x60,
    0x0, 0x4, 0xef, 0xfd, 0x30, 0x0, 0x0, 0x0,
    0x6e, 0xff, 0xfb, 0xbe, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x71,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x4a, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x60, 0x0, 0x15, 0x78, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xa6, 0xdf, 0xff,
    0xff, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xe8, 0x32, 0x5b, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x10,
    0x6, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xf8, 0x7f, 0xd3, 0x9, 0xff, 0xfb, 0x0,
    0x0, 0x1e, 0x70, 0x1, 0xbf, 0xfe, 0xff, 0xf2,
    0x1f, 0xff, 0xf7, 0x0, 0x9, 0xff, 0xa0, 0x0,
    0x8f, 0xff, 0xff, 0x70, 0xdf, 0xff, 0xf1, 0x0,
    0xcf, 0xff, 0xd1, 0x0, 0x5f, 0xff, 0xf9, 0xc,
    0xff, 0xff, 0x30, 0x5, 0xff, 0xff, 0x60, 0x0,
    0x2d, 0xff, 0xb0, 0xef, 0xff, 0xb0, 0x0, 0xa,
    0xff, 0xfc, 0x0, 0x0, 0xa, 0xff, 0xef, 0xff,
    0xe1, 0x0, 0x0, 0xc, 0xff, 0xf6, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x10, 0x0, 0x3, 0xef, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xcb, 0x80,
    0x1, 0xbf, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xdf, 0xfe, 0x70, 0x0, 0x8f, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xcd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x33,
    0x8f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf9, 0x0, 0x6f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfa, 0x0, 0x6f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb,
    0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfd, 0x0, 0x9f,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xcb, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff,
    0xfc, 0x0, 0x2, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x60, 0xa, 0xff, 0xff,
    0xff, 0xfe, 0x42, 0xcf, 0xff, 0xff, 0xff, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x23,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf9, 0x0, 0x12, 0x22, 0x10, 0x0, 0x0, 0x1,
    0x29, 0xff, 0x90, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xfe, 0x20,
    0x5, 0xff, 0xff, 0xff, 0xfd, 0x9a, 0xad, 0xff,
    0xd0, 0x5f, 0xff, 0xbd, 0xff, 0xe2, 0x0, 0x0,
    0xcf, 0x44, 0xff, 0xf9, 0x8, 0xfe, 0x20, 0x0,
    0x0, 0x4, 0x4f, 0xff, 0x90, 0x4, 0xd2, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xa1, 0x91, 0x6,
    0xf6, 0x0, 0x0, 0x2, 0xef, 0xfb, 0xc, 0xfc,
    0x8, 0xff, 0x60, 0xef, 0xff, 0xff, 0xb0, 0x1d,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xfc, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xfe, 0xdd, 0xdd, 0xb0,
    0x0, 0x0, 0x2d, 0xde, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfa, 0xcf, 0xfe, 0x30, 0x0,
    0x0, 0x6f, 0xff, 0x90, 0xc, 0xff, 0xe3, 0x0,
    0x6, 0xff, 0xf9, 0x0, 0x0, 0xcf, 0xfe, 0x30,
    0x5f, 0xff, 0x90, 0x0, 0x0, 0xc, 0xff, 0xe2,
    0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf6,
    0x1b, 0x80, 0x0, 0x0, 0x0, 0x0, 0xb, 0x90,

    /* U+F078 "" */
    0x3, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0x20,
    0x6f, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2,
    0xaf, 0xfe, 0x20, 0x0, 0x0, 0x5, 0xff, 0xf6,
    0x1c, 0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0x90,
    0x1, 0xcf, 0xfe, 0x30, 0x5, 0xff, 0xf9, 0x0,
    0x0, 0x1c, 0xff, 0xe3, 0x5f, 0xff, 0x90, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0x90, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x26, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xa0,
    0x0, 0x44, 0x44, 0x44, 0x44, 0x42, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xfa, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xa0, 0xaf, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0xe, 0xfd, 0xef, 0xcf, 0xf8, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0xb, 0xe2, 0xdf, 0x67,
    0xf5, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x4,
    0x15, 0xfe, 0x3, 0x20, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x6f, 0xd6, 0xfe, 0x4f, 0xf1,
    0x0, 0x0, 0xdf, 0x94, 0x44, 0x44, 0x41, 0x3f,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x24, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x4f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0,

    /* U+F07B "" */
    0x17, 0x88, 0x88, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x44,
    0x44, 0x44, 0x30, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x47, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x11, 0x2f, 0xff, 0xf7, 0x11, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x24, 0x44, 0x42, 0x1f,
    0xff, 0xf6, 0x24, 0x44, 0x42, 0xff, 0xff, 0xfc,
    0x8, 0xbb, 0xa2, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x55, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x66, 0xc4,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xb8,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x90,
    0x0, 0x0, 0x4, 0xa5, 0x0, 0x0, 0xaf, 0xff,
    0xd0, 0x0, 0x0, 0x7d, 0xff, 0xf4, 0x2, 0xcf,
    0xff, 0xe2, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xe9,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xfc, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xfd, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x35, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xfa, 0x0, 0x0, 0x1, 0x9c, 0xa1,
    0xaf, 0xfe, 0xff, 0x60, 0x0, 0x2e, 0xff, 0xf9,
    0xef, 0x60, 0xaf, 0xb0, 0x3, 0xef, 0xff, 0xb0,
    0xef, 0x92, 0xcf, 0x90, 0x3e, 0xff, 0xfa, 0x0,
    0x7f, 0xff, 0xff, 0xe6, 0xff, 0xff, 0xa0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x36, 0xef, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0xaf, 0xfe, 0xff, 0xc2, 0xdf, 0xff, 0xd1, 0x0,
    0xef, 0x60, 0xaf, 0xa0, 0x1c, 0xff, 0xfd, 0x20,
    0xef, 0x92, 0xcf, 0xa0, 0x0, 0xcf, 0xff, 0xe2,
    0x7f, 0xff, 0xff, 0x40, 0x0, 0xb, 0xff, 0xf9,
    0x8, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x58, 0x50,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf1, 0x68, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x7f, 0x80,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x7f, 0xf8,
    0x36, 0x62, 0xaf, 0xff, 0xff, 0xf1, 0x36, 0x66,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xf6, 0x22, 0x22,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf6, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xf7, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x20, 0x0, 0x0,

    /* U+F0C7 "" */
    0x5, 0x66, 0x66, 0x66, 0x66, 0x64, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xef, 0xf9, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf6,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf9,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xc5, 0x7f, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfe, 0x0, 0x5, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfa, 0x0, 0x1, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xfd, 0x0, 0x4, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0x93, 0x4d, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,

    /* U+F0C9 "" */
    0x79, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x95,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x30, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xa1, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x3d, 0xfe, 0x42,
    0xdf, 0xff, 0xff, 0xff, 0xfd, 0x26, 0xff, 0xff,
    0xf9, 0x19, 0xff, 0xff, 0xff, 0x91, 0xbf, 0xff,
    0xff, 0xff, 0xd3, 0x5f, 0xff, 0xe5, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0x99, 0x17, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x77, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+F0E7 "" */
    0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xa5, 0x55, 0x40, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0xef, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x1, 0x22, 0x23, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x8d, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x8b, 0xbc, 0xfa, 0xfd, 0xbb, 0x90, 0x0, 0x0,
    0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x95, 0xff, 0xff, 0xf1, 0x79, 0x0,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf1, 0x7f, 0xb0,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf1, 0x7f, 0xfa,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xf2, 0x2, 0x21,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xdd, 0xdc,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0x88, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x7a, 0xaa, 0x58, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x8, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xcf, 0xfa, 0x40, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f,
    0xf4, 0x4, 0xd0, 0x2f, 0x0, 0xf3, 0x3, 0xf0,
    0xf, 0xf4, 0xff, 0x40, 0x5d, 0x2, 0xf0, 0xf,
    0x40, 0x4f, 0x0, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0x22, 0xb7, 0x29, 0xa2, 0x4f, 0x42, 0xcf,
    0xff, 0x4f, 0xff, 0xf0, 0xa, 0x60, 0x79, 0x2,
    0xf2, 0xb, 0xff, 0xf4, 0xff, 0xff, 0xdd, 0xfe,
    0xdf, 0xfd, 0xef, 0xed, 0xff, 0xff, 0x4f, 0xf8,
    0x48, 0xe4, 0x44, 0x44, 0x44, 0x47, 0xf4, 0x5f,
    0xf4, 0xff, 0x40, 0x4d, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0xff, 0x4f, 0xf7, 0x48, 0xe4, 0x44,
    0x44, 0x44, 0x47, 0xf4, 0x4f, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8b, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x17, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x29,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x56,
    0x66, 0x66, 0x7f, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x58, 0x88, 0x88, 0x87, 0x6, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfe, 0xf, 0x90, 0x0, 0xff, 0xff,
    0xff, 0xfe, 0xf, 0xf9, 0x0, 0xff, 0xff, 0xff,
    0xfe, 0xf, 0xff, 0x90, 0xff, 0xff, 0xff, 0xfe,
    0xf, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0x32,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x14, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x36,
    0x89, 0xa9, 0x74, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc6,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x6, 0xff,
    0xff, 0xfc, 0x75, 0x43, 0x46, 0x9e, 0xff, 0xff,
    0xb1, 0x8, 0xff, 0xfe, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xff, 0xe2, 0xcf, 0xfa, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0x40, 0xb6, 0x0, 0x0, 0x59, 0xde, 0xfe, 0xc7,
    0x20, 0x0, 0x1b, 0x50, 0x0, 0x0, 0x5, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xfe, 0xde, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x40,
    0x0, 0x2, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x1, 0xb6, 0x0, 0x0, 0x0, 0x0, 0x1b, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0x5f, 0xf4, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x3f, 0xf5, 0xff, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xef,
    0x5f, 0xf4, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xf5, 0xff, 0x45, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F241 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x4d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x46, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x30, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F242 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x4c,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x4c, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x46, 0x88, 0x88,
    0x88, 0x80, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F243 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x49,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0x9f, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x49, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0x9f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x44, 0x88, 0x87,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F244 "" */
    0x4, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf3, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x5f,
    0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x45, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x9f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xef, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x2e, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xa1, 0x0, 0xcf, 0xff, 0xd4,
    0x9f, 0x55, 0x55, 0x55, 0x55, 0x55, 0x7f, 0xf7,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xaf, 0xff, 0xa0, 0x0,
    0xb, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xb2, 0x0,
    0x9c, 0x90, 0x0, 0x0, 0x3f, 0x30, 0x0, 0x0,
    0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0x2, 0xbb, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xf9, 0x9f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xbd, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x1, 0x7b, 0xdd, 0xb8, 0x20, 0x0, 0x0,
    0x5f, 0xff, 0xdf, 0xff, 0xf6, 0x0, 0x4, 0xff,
    0xff, 0x68, 0xff, 0xff, 0x40, 0xe, 0xff, 0xff,
    0x60, 0x9f, 0xff, 0xd0, 0x4f, 0xff, 0xff, 0x60,
    0x9, 0xff, 0xf3, 0x8f, 0xf6, 0xbf, 0x61, 0xc0,
    0x9f, 0xf7, 0xbf, 0xf6, 0xb, 0x60, 0xe2, 0x5f,
    0xf9, 0xdf, 0xff, 0x50, 0x20, 0x33, 0xff, 0xfb,
    0xef, 0xff, 0xf5, 0x0, 0x2e, 0xff, 0xfc, 0xef,
    0xff, 0xfc, 0x0, 0x7f, 0xff, 0xfc, 0xdf, 0xff,
    0xd1, 0x0, 0x9, 0xff, 0xfc, 0xcf, 0xfc, 0x14,
    0x50, 0x90, 0xaf, 0xfb, 0xaf, 0xf2, 0x4f, 0x60,
    0xf3, 0x2f, 0xf9, 0x6f, 0xfd, 0xff, 0x70, 0x52,
    0xef, 0xf6, 0x1f, 0xff, 0xff, 0x70, 0x2e, 0xff,
    0xf1, 0x9, 0xff, 0xff, 0x72, 0xef, 0xff, 0x90,
    0x0, 0xbf, 0xff, 0xae, 0xff, 0xfd, 0x10, 0x0,
    0x5, 0xcf, 0xff, 0xfd, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x5, 0x88, 0x88, 0x30, 0x0, 0x0,
    0x56, 0x66, 0x7f, 0xff, 0xff, 0xe6, 0x66, 0x63,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd8,
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0x1f, 0xf6, 0xaf, 0xc4, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0xf, 0xf5, 0x9f, 0xb3, 0xff, 0xa0,
    0xe, 0xff, 0x1f, 0xf6, 0xaf, 0xc4, 0xff, 0xa0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x24, 0x44, 0x44, 0x44, 0x44, 0x31, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x44, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x44, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0x44, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0x41, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x57, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x72, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0xbf, 0xff, 0xff, 0xf8,
    0xaf, 0xff, 0xa8, 0xff, 0xff, 0xf8, 0x0, 0xbf,
    0xff, 0xff, 0xfa, 0x0, 0xaf, 0xa0, 0xa, 0xff,
    0xff, 0x80, 0xbf, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x60, 0x3, 0xff, 0xff, 0xf8, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x9f, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xff, 0xb0, 0x3, 0xe3, 0x0,
    0xbf, 0xff, 0xf8, 0x0, 0x4f, 0xff, 0xff, 0xfe,
    0x23, 0xff, 0xf3, 0x2e, 0xff, 0xff, 0x80, 0x0,
    0x4f, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90,

    /* U+F7C2 "" */
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xcf,
    0x47, 0xf4, 0xd8, 0x4f, 0xf5, 0xc, 0xff, 0x3,
    0xe0, 0xc5, 0xe, 0xf5, 0xcf, 0xff, 0x3, 0xe0,
    0xc5, 0xe, 0xf5, 0xff, 0xff, 0x24, 0xe2, 0xc6,
    0x2e, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x1, 0x34,
    0x44, 0x44, 0x44, 0x42, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x10, 0x0, 0x3e, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0, 0x4f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10,
    0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x3e, 0xff, 0xfe, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xca, 0x0, 0x2e, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 77, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 77, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 20, .adv_w = 113, .box_w = 5, .box_h = 6, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 35, .adv_w = 202, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 120, .adv_w = 179, .box_w = 11, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 219, .adv_w = 243, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 317, .adv_w = 198, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 408, .adv_w = 60, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 414, .adv_w = 97, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 459, .adv_w = 97, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 504, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 529, .adv_w = 168, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 570, .adv_w = 65, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 582, .adv_w = 110, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 587, .adv_w = 65, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 593, .adv_w = 101, .box_w = 8, .box_h = 18, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 665, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 743, .adv_w = 107, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 776, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 841, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 906, .adv_w = 193, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 984, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1049, .adv_w = 178, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1121, .adv_w = 172, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1193, .adv_w = 185, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1265, .adv_w = 178, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1337, .adv_w = 65, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1357, .adv_w = 65, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1383, .adv_w = 168, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1424, .adv_w = 168, .box_w = 9, .box_h = 7, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 1456, .adv_w = 168, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1497, .adv_w = 165, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1562, .adv_w = 298, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1715, .adv_w = 211, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1813, .adv_w = 218, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1891, .adv_w = 208, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1976, .adv_w = 238, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2061, .adv_w = 193, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2133, .adv_w = 183, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2198, .adv_w = 222, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2283, .adv_w = 234, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2361, .adv_w = 89, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2381, .adv_w = 148, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2440, .adv_w = 207, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2518, .adv_w = 171, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2583, .adv_w = 275, .box_w = 15, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2681, .adv_w = 234, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2759, .adv_w = 242, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2857, .adv_w = 208, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2935, .adv_w = 242, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3055, .adv_w = 209, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3133, .adv_w = 179, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3205, .adv_w = 169, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3277, .adv_w = 228, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3355, .adv_w = 205, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3446, .adv_w = 324, .box_w = 20, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3576, .adv_w = 194, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3654, .adv_w = 186, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3739, .adv_w = 189, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3817, .adv_w = 96, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3862, .adv_w = 101, .box_w = 8, .box_h = 18, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3934, .adv_w = 96, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3979, .adv_w = 168, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4015, .adv_w = 144, .box_w = 9, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4024, .adv_w = 173, .box_w = 5, .box_h = 3, .ofs_x = 2, .ofs_y = 11},
    {.bitmap_index = 4032, .adv_w = 172, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4082, .adv_w = 196, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4159, .adv_w = 164, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4209, .adv_w = 196, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4286, .adv_w = 176, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4341, .adv_w = 102, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4390, .adv_w = 199, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4467, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4537, .adv_w = 80, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4558, .adv_w = 82, .box_w = 6, .box_h = 18, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 4612, .adv_w = 177, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4689, .adv_w = 80, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4710, .adv_w = 304, .box_w = 17, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4795, .adv_w = 196, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4845, .adv_w = 183, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4900, .adv_w = 196, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4977, .adv_w = 196, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5054, .adv_w = 118, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5084, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5129, .adv_w = 119, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5171, .adv_w = 195, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5221, .adv_w = 161, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5281, .adv_w = 259, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5366, .adv_w = 159, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5416, .adv_w = 161, .box_w = 12, .box_h = 14, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 5500, .adv_w = 150, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5545, .adv_w = 101, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5590, .adv_w = 86, .box_w = 3, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5617, .adv_w = 101, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5671, .adv_w = 168, .box_w = 9, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 5694, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 5719, .adv_w = 90, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 5727, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5908, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6034, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6187, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6313, .adv_w = 198, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6398, .adv_w = 288, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6569, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6740, .adv_w = 324, .box_w = 21, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6919, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7090, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7237, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7408, .adv_w = 144, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7476, .adv_w = 216, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7581, .adv_w = 324, .box_w = 21, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7770, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7896, .adv_w = 198, .box_w = 13, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8020, .adv_w = 252, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8122, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8274, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8410, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8546, .adv_w = 252, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8648, .adv_w = 252, .box_w = 18, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8801, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8889, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8977, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9113, .adv_w = 252, .box_w = 16, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 9145, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9292, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9511, .adv_w = 324, .box_w = 22, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9720, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9873, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 9953, .adv_w = 252, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 10033, .adv_w = 360, .box_w = 24, .box_h = 15, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 10213, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10339, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10510, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10691, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10827, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10979, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11115, .adv_w = 252, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11235, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11361, .adv_w = 180, .box_w = 13, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11485, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11637, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11789, .adv_w = 324, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11936, .adv_w = 288, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12136, .adv_w = 216, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12269, .adv_w = 360, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12476, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12614, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12752, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12890, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13028, .adv_w = 360, .box_w = 23, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13166, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13339, .adv_w = 252, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13472, .adv_w = 252, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13624, .adv_w = 288, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13805, .adv_w = 360, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13966, .adv_w = 216, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14099, .adv_w = 290, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 3, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 13, 0, 8, -6, 0, 0,
    0, 0, -16, -17, 2, 14, 6, 5,
    -12, 2, 14, 1, 12, 3, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 17, 2, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 0, -9, 0, 0, 0, 0,
    0, -6, 5, 6, 0, 0, -3, 0,
    -2, 3, 0, -3, 0, -3, -1, -6,
    0, 0, 0, 0, -3, 0, 0, -4,
    -4, 0, 0, -3, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -3, 0, -4, 0, -8, 0, -35, 0,
    0, -6, 0, 6, 9, 0, 0, -6,
    3, 3, 10, 6, -5, 6, 0, 0,
    -16, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -11, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, -3, -14, 0, -12,
    -2, 0, 0, 0, 0, 1, 11, 0,
    -9, -2, -1, 1, 0, -5, 0, 0,
    -2, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -23, -2, 11,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 10,
    0, 3, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 11, 2,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    6, 3, 9, -3, 0, 0, 6, -3,
    -10, -39, 2, 8, 6, 1, -4, 0,
    10, 0, 9, 0, 9, 0, -27, 0,
    -3, 9, 0, 10, -3, 6, 3, 0,
    0, 1, -3, 0, 0, -5, 23, 0,
    23, 0, 9, 0, 12, 4, 5, 9,
    0, 0, 0, -11, 0, 0, 0, 0,
    1, -2, 0, 2, -5, -4, -6, 2,
    0, -3, 0, 0, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -16, 0, -18, 0, 0, 0,
    0, -2, 0, 29, -3, -4, 3, 3,
    -3, 0, -4, 3, 0, 0, -15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -28, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -18, 0, 17, 0, 0, -11, 0,
    10, 0, -20, -28, -20, -6, 9, 0,
    0, -19, 0, 3, -7, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 7, 9, -35, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 14, 0, 2, 0, 0, 0,
    0, 0, 2, 2, -3, -6, 0, -1,
    -1, -3, 0, 0, -2, 0, 0, 0,
    -6, 0, -2, 0, -7, -6, 0, -7,
    -10, -10, -5, 0, -6, 0, -6, 0,
    0, 0, 0, -2, 0, 0, 3, 0,
    2, -3, 0, 1, 0, 0, 0, 3,
    -2, 0, 0, 0, -2, 3, 3, -1,
    0, 0, 0, -5, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 4, -2, 0,
    -3, 0, -5, 0, 0, -2, 0, 9,
    0, 0, -3, 0, 0, 0, 0, 0,
    -1, 1, -2, -2, 0, 0, -3, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, -1, 0, -3, -3, 0,
    0, 0, 0, 0, 1, 0, 0, -2,
    0, -3, -3, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -2, -4, 0, -4, 0, -9,
    -2, -9, 6, 0, 0, -6, 3, 6,
    8, 0, -7, -1, -3, 0, -1, -14,
    3, -2, 2, -15, 3, 0, 0, 1,
    -15, 0, -15, -2, -25, -2, 0, -14,
    0, 6, 8, 0, 4, 0, 0, 0,
    0, 1, 0, -5, -4, 0, -9, 0,
    0, 0, -3, 0, 0, 0, -3, 0,
    0, 0, 0, 0, -1, -1, 0, -1,
    -4, 0, 0, 0, 0, 0, 0, 0,
    -3, -3, 0, -2, -3, -2, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -3,
    0, -2, 0, -6, 3, 0, 0, -3,
    1, 3, 3, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -3, 0, -3, -2, -3, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -3, -4, 0,
    -5, 0, 9, -2, 1, -9, 0, 0,
    8, -14, -15, -12, -6, 3, 0, -2,
    -19, -5, 0, -5, 0, -6, 4, -5,
    -18, 0, -8, 0, 0, 1, -1, 2,
    -2, 0, 3, 0, -9, -11, 0, -14,
    -7, -6, -7, -9, -3, -8, -1, -5,
    -8, 2, 0, 1, 0, -3, 0, 0,
    0, 2, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, -1, 0, -1, -3, 0, -5, -6,
    -6, -1, 0, -9, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 1,
    -2, 0, 0, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 14, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -5, 0, 0, 0, 0, -14, -9, 0,
    0, 0, -4, -14, 0, 0, -3, 3,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, 0, -5, 0,
    0, 0, 0, 3, 0, 2, -6, -6,
    0, -3, -3, -3, 0, 0, 0, 0,
    0, 0, -9, 0, -3, 0, -4, -3,
    0, -6, -7, -9, -2, 0, -6, 0,
    -9, 0, 0, 0, 0, 23, 0, 0,
    1, 0, 0, -4, 0, 3, 0, -12,
    0, 0, 0, 0, 0, -27, -5, 10,
    9, -2, -12, 0, 3, -4, 0, -14,
    -1, -4, 3, -20, -3, 4, 0, 4,
    -10, -4, -11, -10, -12, 0, 0, -17,
    0, 16, 0, 0, -1, 0, 0, 0,
    -1, -1, -3, -8, -10, -1, -27, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -1, -3, -4, 0, 0,
    -6, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -6, 0, 0, 6,
    -1, 4, 0, -6, 3, -2, -1, -7,
    -3, 0, -4, -3, -2, 0, -4, -5,
    0, 0, -2, -1, -2, -5, -3, 0,
    0, -3, 0, 3, -2, 0, -6, 0,
    0, 0, -6, 0, -5, 0, -5, -5,
    3, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 3, 0, -4, 0, -2, -3,
    -9, -2, -2, -2, -1, -2, -3, -1,
    0, 0, 0, 0, 0, -3, -2, -2,
    0, 0, 0, 0, 3, -2, 0, -2,
    0, 0, 0, -2, -3, -2, -3, -3,
    -3, 0, 2, 12, -1, 0, -8, 0,
    -2, 6, 0, -3, -12, -4, 4, 0,
    0, -14, -5, 3, -5, 2, 0, -2,
    -2, -9, 0, -4, 1, 0, 0, -5,
    0, 0, 0, 3, 3, -6, -5, 0,
    -5, -3, -4, -3, -3, 0, -5, 1,
    -5, -5, 9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, -4,
    0, 0, -3, -3, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -4, 0, -6, 0, 0, 0, -10, 0,
    2, -6, 6, 1, -2, -14, 0, 0,
    -6, -3, 0, -12, -7, -8, 0, 0,
    -12, -3, -12, -11, -14, 0, -7, 0,
    2, 19, -4, 0, -7, -3, -1, -3,
    -5, -8, -5, -11, -12, -7, -3, 0,
    0, -2, 0, 1, 0, 0, -20, -3,
    9, 6, -6, -11, 0, 1, -9, 0,
    -14, -2, -3, 6, -26, -4, 1, 0,
    0, -19, -3, -15, -3, -21, 0, 0,
    -20, 0, 17, 1, 0, -2, 0, 0,
    0, 0, -1, -2, -11, -2, 0, -19,
    0, 0, 0, 0, -9, 0, -3, 0,
    -1, -8, -14, 0, 0, -1, -4, -9,
    -3, 0, -2, 0, 0, 0, 0, -13,
    -3, -10, -9, -2, -5, -7, -3, -5,
    0, -6, -3, -10, -4, 0, -3, -5,
    -3, -5, 0, 1, 0, -2, -10, 0,
    6, 0, -5, 0, 0, 0, 0, 3,
    0, 2, -6, 12, 0, -3, -3, -3,
    0, 0, 0, 0, 0, 0, -9, 0,
    -3, 0, -4, -3, 0, -6, -7, -9,
    -2, 0, -6, 2, 12, 0, 0, 0,
    0, 23, 0, 0, 1, 0, 0, -4,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -2, -6, 0, 0, 0, 0, 0, -1,
    0, 0, 0, -3, -3, 0, 0, -6,
    -3, 0, 0, -6, 0, 5, -1, 0,
    0, 0, 0, 0, 0, 1, 0, 0,
    0, 0, 4, 6, 2, -3, 0, -9,
    -5, 0, 9, -10, -9, -6, -6, 12,
    5, 3, -25, -2, 6, -3, 0, -3,
    3, -3, -10, 0, -3, 3, -4, -2,
    -9, -2, 0, 0, 9, 6, 0, -8,
    0, -16, -4, 8, -4, -11, 1, -4,
    -10, -10, -3, 12, 3, 0, -4, 0,
    -8, 0, 2, 10, -7, -11, -12, -7,
    9, 0, 1, -21, -2, 3, -5, -2,
    -7, 0, -6, -11, -4, -4, -2, 0,
    0, -7, -6, -3, 0, 9, 7, -3,
    -16, 0, -16, -4, 0, -10, -17, -1,
    -9, -5, -10, -8, 8, 0, 0, -4,
    0, -6, -3, 0, -3, -5, 0, 5,
    -10, 3, 0, 0, -15, 0, -3, -6,
    -5, -2, -9, -7, -10, -7, 0, -9,
    -3, -7, -5, -9, -3, 0, 0, 1,
    14, -5, 0, -9, -3, 0, -3, -6,
    -7, -8, -8, -11, -4, -6, 6, 0,
    -4, 0, -14, -3, 2, 6, -9, -11,
    -6, -10, 10, -3, 1, -27, -5, 6,
    -6, -5, -11, 0, -9, -12, -3, -3,
    -2, -3, -6, -9, -1, 0, 0, 9,
    8, -2, -19, 0, -17, -7, 7, -11,
    -20, -6, -10, -12, -14, -10, 6, 0,
    0, 0, 0, -3, 0, 0, 3, -3,
    6, 2, -5, 6, 0, 0, -9, -1,
    0, -1, 0, 1, 1, -2, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 2, 9, 1, 0, -3, 0, 0,
    0, 0, -2, -2, -3, 0, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 11, 0, 5, 1, 1, -4,
    0, 6, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 9, 0, 8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -17, 0, -3, 5, 0, 9,
    0, 0, 29, 3, -6, -6, 3, 3,
    -2, 1, -14, 0, 0, 14, -17, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -20, 11, 40, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, -5,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, -8, 0,
    0, 1, 0, 0, 3, 37, -6, -2,
    9, 8, -8, 3, 0, 0, 3, 3,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -37, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, 0, 0, -8, 0, 0, 0, 0,
    -6, -1, 0, 0, 0, -6, 0, -3,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -19, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -3, 0, 0, -5, 0, -4, 0,
    -8, 0, 0, 0, -5, 3, -3, 0,
    0, -8, -3, -7, 0, 0, -8, 0,
    -3, 0, -14, 0, -3, 0, 0, -23,
    -5, -12, -3, -10, 0, 0, -19, 0,
    -8, -1, 0, 0, 0, 0, 0, 0,
    0, 0, -4, -5, -2, -5, 0, 0,
    0, 0, -6, 0, -6, 4, -3, 6,
    0, -2, -7, -2, -5, -5, 0, -3,
    -1, -2, 2, -8, -1, 0, 0, 0,
    -25, -2, -4, 0, -6, 0, -2, -14,
    -3, 0, 0, -2, -2, 0, 0, 0,
    0, 2, 0, -2, -5, -2, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, 0,
    0, -6, 0, -2, 0, 0, 0, -6,
    3, 0, 0, 0, -8, -3, -6, 0,
    0, -8, 0, -3, 0, -14, 0, 0,
    0, 0, -28, 0, -6, -11, -14, 0,
    0, -19, 0, -2, -4, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -4, -1,
    -4, 1, 0, 0, 5, -4, 0, 9,
    14, -3, -3, -9, 3, 14, 5, 6,
    -8, 3, 12, 3, 8, 6, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 18, 14, -5, -3, 0, -2,
    23, 12, 23, 0, 0, 0, 3, 0,
    0, 11, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, 0, 0, 0, -24, -3, -2, -12,
    -14, 0, 0, -19, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    4, 0, 0, 0, 0, -24, -3, -2,
    -12, -14, 0, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -7, 3, 0, -3,
    2, 5, 3, -9, 0, -1, -2, 3,
    0, 2, 0, 0, 0, 0, -7, 0,
    -3, -2, -6, 0, -3, -12, 0, 18,
    -3, 0, -6, -2, 0, -2, -5, 0,
    -3, -8, -6, -3, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, -24,
    -3, -2, -12, -14, 0, 0, -19, 0,
    0, 0, 0, 0, 0, 14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, -9, -3, -3, 9, -3, -3,
    -12, 1, -2, 1, -2, -8, 1, 6,
    1, 2, 1, 2, -7, -12, -3, 0,
    -11, -5, -8, -12, -11, 0, -5, -6,
    -3, -4, -2, -2, -3, -2, 0, -2,
    -1, 4, 0, 4, -2, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -3, -3, 0, 0,
    -8, 0, -1, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -17, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, 0, -4,
    0, 0, 0, 0, -2, 0, 0, -5,
    -3, 3, 0, -5, -5, -2, 0, -8,
    -2, -6, -2, -3, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 9, 0, 0, -5, 0,
    0, 0, 0, -4, 0, -3, 0, 0,
    -1, 0, 0, -2, 0, -7, 0, 0,
    12, -4, -10, -9, 2, 3, 3, -1,
    -8, 2, 4, 2, 9, 2, 10, -2,
    -8, 0, 0, -12, 0, 0, -9, -8,
    0, 0, -6, 0, -4, -5, 0, -4,
    0, -4, 0, -2, 4, 0, -2, -9,
    -3, 11, 0, 0, -3, 0, -6, 0,
    0, 4, -7, 0, 3, -3, 2, 0,
    0, -10, 0, -2, -1, 0, -3, 3,
    -2, 0, 0, 0, -12, -3, -6, 0,
    -9, 0, 0, -14, 0, 11, -3, 0,
    -5, 0, 2, 0, -3, 0, -3, -9,
    0, -3, 3, 0, 0, 0, 0, -2,
    0, 0, 3, -4, 1, 0, 0, -3,
    -2, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -18, 0, 6, 0,
    0, -2, 0, 0, 0, 0, 1, 0,
    -3, -3, 0, 0, 0, 6, 0, 7,
    0, 0, 0, 0, 0, -18, -16, 1,
    12, 9, 5, -12, 2, 12, 0, 11,
    0, 6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_18 = {
#else
lv_font_t lv_font_montserrat_18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 21,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRAT_18*/

