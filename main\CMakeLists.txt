# Optimized source file collection - removed unused LVGL components
file(GLOB_RECURSE SOURCES "./*.c"
    "./gui/lvgl/src/*.c"
    "./hardware/*.c"
    "./gui/lvgl_app/*.c"
    "./gui/lvgl/src/core/*.c"
    "./gui/lvgl/src/draw/*.c"
    "./gui/lvgl/src/font/*.c"
    "./gui/lvgl/src/hal/*.c"
    "./gui/lvgl/src/misc/*.c"
    "./gui/lvgl/src/widgets/*.c"
    "./gui/lvgl/src/extra/*.c"
    "./gui/lvgl/src/extra/layouts/*.c"
    "./gui/lvgl/src/extra/layouts/flex/*.c"  # Keep flex layout
    "./gui/lvgl/src/extra/themes/default/*.c"  # Keep only default theme
    "./gui/lvgl/src/extra/widgets/imgbtn/*.c"  # Keep only used widgets
    "./gui/lvgl_app/*.c"
    "./gui/lvgl_app/resources/*.c"
    "./gui/lvgl_driver/*.c"
    "./driver/*.c"
)
set(INCLUDE_DIRSx "./"
    "./hardware/"
    "./gui/"
    "./gui/lvgl/"
    "./gui/lvgl/src/"
    "./gui/lvgl/src/core/"
    "./gui/lvgl/src/draw/"
    "./gui/lvgl/src/extra/"
    "./gui/lvgl/src/font/"
    "./gui/lvgl/src/gpu/"
    "./gui/lvgl/src/hal/"
    "./gui/lvgl/src/misc/"
    "./gui/lvgl/src/widgets/"
    "./gui/lvgl/src/extra/layouts/"
    "./gui/lvgl/src/extra/layouts/flex/"
    "./gui/lvgl/src/extra/layouts/grid/"
    "./gui/lvgl/src/extra/themes/"
    "./gui/lvgl/src/extra/themes/basic/"
    "./gui/lvgl/src/extra/themes/default/"
    "./gui/lvgl/src/extra/widgets/calendar/"
    "./gui/lvgl/src/extra/widgets/colorwheel/"
    "./gui/lvgl/src/extra/widgets/"
    "./gui/lvgl/src/extra/widgets/imgbtn/"
    "./gui/lvgl/src/extra/widgets/keyboard/"
    "./gui/lvgl/src/extra/widgets/led/"
    "./gui/lvgl/src/extra/widgets/list/"
    "./gui/lvgl/src/extra/widgets/msgbox/"
    "./gui/lvgl/src/extra/widgets/spinbox/"
    "./gui/lvgl/src/extra/widgets/spinner/"
    "./gui/lvgl/src/extra/widgets/tabview/"
    "./gui/lvgl/src/extra/widgets/tileview/"
    "./gui/lvgl/src/extra/widgets/win/"
    "./gui/lvgl_app/"
    "./gui/lvgl_app/resources/"
    "./gui/lvgl_driver/"
    "./driver/"
)

set(SRCSx
    main.c
    hardware/msp.c
)

idf_component_register(SRCS ${SRCSx} ${SOURCES} INCLUDE_DIRS ${INCLUDE_DIRSx})

# ESP32 Performance Optimizations
target_compile_options(${COMPONENT_LIB} PRIVATE
    -ffast-math
    -funroll-loops
    -finline-functions
    -fno-stack-protector  # Disable stack protection for performance
    -Os  # Optimize for size
)

# Link-time optimization for further size reduction
target_link_options(${COMPONENT_LIB} PRIVATE
    -flto
    -Wl,--gc-sections  # Remove unused sections
)

# idf_component_register(SRCS "main.c" INCLUDE_DIRS "")
