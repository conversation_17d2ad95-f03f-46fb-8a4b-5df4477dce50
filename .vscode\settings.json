{"idf.adapterTargetName": "esp32", "idf.portWin": "COM3", "idf.flashType": "UART", "files.associations": {"rx5808.h": "c", "string.h": "c", "24cxx.h": "c", "sdkconfig.h": "c", "lcd.h": "c", "page_menu.h": "c", "rx5808_config.h": "c", "hwvers.h": "c", "bitmap.h": "c", "capi_video.h": "c", "spi.h": "c", "page_main.h": "c", "backpack.h": "c", "xutility": "c", "compare": "c", "cstddef": "c", "limits": "c", "type_traits": "c", "vector": "c", "xmemory": "c", "xstring": "c", "page_backpack_handler.h": "c", "lv_port_disp.h": "c", "lvgl.h": "c", "lv_mem.h": "c", "lv_assert.h": "c", "lv_refr.h": "c", "utility": "cpp", "initializer_list": "cpp", "stdint.h": "c", "stdbool.h": "c", "xtr1common": "c", "stdio.h": "c", "lvgl_init.h": "c", "cstdlib": "c", "tuple": "c", "beep.h": "c", "system.h": "c", "page_start.h": "c", "page_setup.h": "c", "lv_port_indev.h": "c", "lvgl_stl.h": "c", "stdlib.h": "c", "page_about.h": "c", "math.h": "c", "lv_obj.h": "c", "lv_math.h": "c", "lv_group.h": "c", "msp_protocol.h": "c", "msp.h": "c", "lv_async.h": "c", "iosfwd": "c", "lv_obj_tree.h": "c", "lv_style.h": "c", "lv_types.h": "c", "osd_font.h": "c", "page_scan.h": "c", "page_scan_calib.h": "c", "bit": "c", "new": "c", "page_scan_chart.h": "c", "cstdint": "c", "page_scan_table.h": "c", "array": "c", "string": "c", "string_view": "c", "random": "c"}, "idf.openOcdConfigs": ["board/esp32-wrover-kit-3.3v.cfg"], "idf.espIdfPathWin": "D:/Espressif/frameworks/esp-idf-v5.3.1/", "idf.pythonBinPathWin": "C:\\Users\\<USER>\\.espressif\\python_env\\idf4.4_py3.8_env\\Scripts\\python.exe", "idf.toolsPathWin": "D:\\Espressif", "idf.customExtraPaths": "C:\\Users\\<USER>\\.espressif\\tools\\xtensa-esp-elf-gdb\\11.2_20220823\\xtensa-esp-elf-gdb\\bin;C:\\Users\\<USER>\\.espressif\\tools\\riscv32-esp-elf-gdb\\11.2_20220823\\riscv32-esp-elf-gdb\\bin;C:\\Users\\<USER>\\.espressif\\tools\\xtensa-esp32-elf\\esp-2021r2-patch5-8.4.0\\xtensa-esp32-elf\\bin;C:\\Users\\<USER>\\.espressif\\tools\\xtensa-esp32s2-elf\\esp-2021r2-patch5-8.4.0\\xtensa-esp32s2-elf\\bin;C:\\Users\\<USER>\\.espressif\\tools\\xtensa-esp32s3-elf\\esp-2021r2-patch5-8.4.0\\xtensa-esp32s3-elf\\bin;C:\\Users\\<USER>\\.espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch5-8.4.0\\riscv32-esp-elf\\bin;C:\\Users\\<USER>\\.espressif\\tools\\esp32ulp-elf\\2.35_20220830\\esp32ulp-elf\\bin;C:\\Users\\<USER>\\.espressif\\tools\\cmake\\3.23.1\\bin;C:\\Users\\<USER>\\.espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921\\openocd-esp32\\bin;C:\\Users\\<USER>\\.espressif\\tools\\ninja\\1.10.2;C:\\Users\\<USER>\\.espressif\\tools\\idf-exe\\1.0.3;C:\\Users\\<USER>\\.espressif\\tools\\ccache\\4.3\\ccache-4.3-windows-64;C:\\Users\\<USER>\\.espressif\\tools\\dfu-util\\0.9\\dfu-util-0.9-win64", "idf.customExtraVars": {"OPENOCD_SCRIPTS": "C:\\Users\\<USER>\\.espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20230921/openocd-esp32/share/openocd/scripts", "IDF_CCACHE_ENABLE": "1", "ESP_ROM_ELF_DIR": "D:\\Espressif\\tools\\esp-rom-elfs\\20240305/", "IDF_TARGET": "esp32"}, "idf.gitPathWin": "C:\\Users\\<USER>\\.espressif\\tools\\idf-git\\2.39.2\\cmd\\git.exe", "idf.pythonInstallPath": "D:\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "C_Cpp.default.compilerPath": "C:/mingw64/bin/gcc.exe"}