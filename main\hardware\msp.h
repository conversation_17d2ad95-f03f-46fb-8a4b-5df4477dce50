/**
 * @file msp.h
 * @brief MSP协议模块头文件
 *
 * 定义了MSP V2协议的接口和命令
 */

#ifndef MSP_H
#define MSP_H

#include <stdint.h>
#include <stdbool.h>

#define MSP_TAG "MSP"

#define RX_BUF_SIZE (1024)
#define TX_BUF_SIZE (512)

// MSP V2
#define MSP_V2_FRAME_START '$'
#define MSP_V2_MARKER 'X'
#define MSP_V2_TYPE_IN '<'  // 输入
#define MSP_V2_TYPE_OUT '>' // 输出
#define MSP_V2_TYPE_ERR '!'

// MSP命令
#define MSP_ELRS_BACKPACK_GET_CHANNEL_INDEX         0x0300
#define MSP_ELRS_BACKPACK_SET_CHANNEL_INDEX         0x0301
#define MSP_ELRS_BACKPACK_GET_VERSION               0x0381 // get the bacpack firmware version
#define MSP_ELRS_BACKPACK_GET_STATUS                0x0382  // get the status of the backpack

// MSP 状态机状态
typedef enum
{
    MSP_IDLE,               // 等待帧起始
    MSP_HEADER_START,       // 收到 $
    MSP_HEADER_X,           // 收到 X
    MSP_HEADER_TYPE,        // 收到 < 或 > 或 !
    MSP_HEADER_FLAG,        // 收到 flag
    MSP_HEADER_FUNCTION_LO, // 收到 function 低字节
    MSP_HEADER_FUNCTION_HI, // 收到 function 高字节
    MSP_HEADER_SIZE_LO,     // 收到 size 低字节
    MSP_HEADER_SIZE_HI,     // 收到 size 高字节
    MSP_PAYLOAD,            // 接收 payload
    MSP_CHECKSUM,           // 接收 checksum
    MSP_COMMAND_RECEIVED    // 命令接收完成
} msp_state_e;

// MSP 帧结构
typedef struct
{
    uint8_t type;                 // < > !
    uint8_t flags;                // 标志位
    uint16_t function;            // 功能码
    uint16_t payload_size;        // 负载大小
    uint8_t payload[RX_BUF_SIZE]; // 负载数据
    uint8_t crc;                  // 校验和
} msp_packet_t;

typedef void (*msp_callback_t)(msp_packet_t *packet);

/**
 * @brief 初始化MSP协议处理
 *
 * @param callback 接收到完整MSP包后的回调函数
 */
void MSP_init(msp_callback_t callback);

/**
 * @brief 设置发送数据的函数
 *
 * @param func 发送数据的函数指针
 */
void MSP_set_send_function(void (*func)(uint8_t *data, int len));

/**
 * @brief 处理接收到的字节，实现MSP协议状态机
 *
 * @param c 接收到的字节
 * @return true 如果一个完整的MSP包已接收
 * @return false 如果MSP包尚未接收完成
 */
bool MSP_process_byte(uint8_t c);

/**
 * @brief 处理完整的MSP数据包
 */
void MSP_parse_completed_packet(void);

/**
 * @brief 发送MSP命令
 *
 * @param function 功能码
 * @param payload 负载数据
 * @param payload_size 负载大小
 */
void MSP_send_command(uint16_t function, uint8_t *payload, uint16_t payload_size);

/**
 * @brief 发送MSP响应
 *
 * @param function 功能码
 * @param payload 负载数据
 * @param payload_size 负载大小
 */
void MSP_send_response(uint16_t function, uint8_t *payload, uint16_t payload_size);

/**
 * @brief 计算MSP CRC校验和
 *
 * @param crc 初始CRC值
 * @param a 要计算的字节
 * @return uint8_t 计算后的CRC值
 */
uint8_t MSP_crc8_dvb_s2(uint8_t crc, uint8_t a);

#endif // MSP_H
