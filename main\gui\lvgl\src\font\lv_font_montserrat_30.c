/*******************************************************************************
 * Size: 30 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 30 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_30.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_30
    #define LV_FONT_MONTSERRAT_30 1
#endif

#if LV_FONT_MONTSERRAT_30

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xaf, 0xfb, 0x9f, 0xfa, 0x9f, 0xfa, 0x8f, 0xf9,
    0x7f, 0xf8, 0x7f, 0xf8, 0x6f, 0xf7, 0x6f, 0xf6,
    0x5f, 0xf6, 0x4f, 0xf5, 0x4f, 0xf4, 0x3f, 0xf4,
    0x3f, 0xf3, 0x2f, 0xf3, 0x5, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xb3, 0xcf, 0xfd, 0xdf, 0xfe,
    0x4e, 0xe4,

    /* U+0022 "\"" */
    0x1f, 0xf6, 0x0, 0xaf, 0xd1, 0xff, 0x50, 0xa,
    0xfc, 0xf, 0xf5, 0x0, 0xaf, 0xc0, 0xff, 0x50,
    0x9, 0xfb, 0xf, 0xf4, 0x0, 0x9f, 0xb0, 0xff,
    0x40, 0x9, 0xfb, 0xf, 0xf3, 0x0, 0x8f, 0xa0,
    0xff, 0x30, 0x8, 0xfa, 0x1, 0x10, 0x0, 0x1,
    0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x6f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0x0,
    0x0, 0x8, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0xc, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x2, 0x22, 0x3f, 0xf3, 0x22, 0x22,
    0x6f, 0xd2, 0x22, 0x20, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0xa,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90,
    0x0, 0x0, 0xbf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf7, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x22, 0x22, 0xcf, 0x72, 0x22, 0x22, 0xff,
    0x52, 0x22, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x5f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0x7, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xb0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf9, 0x0, 0x0, 0xb, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x3, 0x83, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xff, 0xec,
    0x83, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x20, 0x2, 0xef, 0xff, 0xcc, 0xfc,
    0xbe, 0xff, 0xf3, 0x0, 0xbf, 0xfc, 0x20, 0x7f,
    0x70, 0x2, 0x9c, 0x0, 0x1f, 0xff, 0x10, 0x7,
    0xf7, 0x0, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x7, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xfd, 0x69, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xcf, 0xff, 0xff, 0xff, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xcf, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf9, 0x8e, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x7, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0xb,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0,
    0x9f, 0xf5, 0x8, 0x10, 0x0, 0x7, 0xf7, 0x0,
    0xd, 0xff, 0x34, 0xfe, 0x71, 0x0, 0x7f, 0x70,
    0xa, 0xff, 0xd0, 0x8f, 0xff, 0xfc, 0xac, 0xfb,
    0xbf, 0xff, 0xf3, 0x0, 0x5d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x4, 0x9d, 0xef,
    0xff, 0xeb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x5c, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfa, 0x0, 0x0, 0x7, 0xff, 0xcd, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x3f, 0xe3, 0x0, 0x3e, 0xf2, 0x0, 0x0, 0x1,
    0xff, 0x40, 0x0, 0x0, 0x9f, 0x70, 0x0, 0x7,
    0xf9, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0xdf, 0x30, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x6f,
    0xd0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x1,
    0xfd, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xdf, 0x30, 0x0, 0x3, 0xfc, 0x0, 0xc, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x70, 0x0, 0x7,
    0xf9, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xe2, 0x0, 0x3e, 0xf2, 0x2, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xcc, 0xff,
    0x70, 0xc, 0xf7, 0x0, 0x59, 0xa8, 0x20, 0x0,
    0x0, 0x5c, 0xff, 0xc5, 0x0, 0x8f, 0xc0, 0xc,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x20, 0xaf, 0xb2, 0x5, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf6, 0x1, 0xfe,
    0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x6, 0xf9, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x8, 0xf6,
    0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0xe,
    0xf6, 0x0, 0x8, 0xf6, 0x0, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x7, 0xf7,
    0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x4, 0xfe,
    0x10, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x3f, 0xd0,
    0x0, 0x0, 0x1e, 0xf5, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0xbf, 0x60, 0x0, 0x0, 0xaf, 0xa0,
    0x0, 0x0, 0x0, 0x2e, 0xfa, 0x9d, 0xfa, 0x0,
    0x0, 0x5, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xae, 0xfd, 0x70, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x5, 0xbe, 0xfe, 0xc6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb4,
    0x23, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfd, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfa, 0x0, 0x0, 0xa, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x1e,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x50,
    0x1, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf3, 0x5e, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfc, 0x35, 0xff, 0xf4, 0x0, 0x4, 0xc7, 0x0,
    0x9, 0xff, 0x90, 0x0, 0x4f, 0xff, 0x40, 0x9,
    0xfe, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x4, 0xff,
    0xf5, 0xe, 0xf9, 0x0, 0x8f, 0xf5, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xaf, 0xf4, 0x0, 0xaf, 0xf3,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xc0, 0x0,
    0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x80, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x4,
    0xdf, 0xff, 0xf6, 0x0, 0x9, 0xff, 0xfd, 0x86,
    0x78, 0xdf, 0xff, 0x8e, 0xff, 0x70, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x2, 0xef, 0xc0,
    0x0, 0x2, 0x7c, 0xef, 0xed, 0x95, 0x0, 0x0,
    0x2c, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x1f, 0xf6, 0x1f, 0xf5, 0xf, 0xf5, 0xf, 0xf5,
    0xf, 0xf4, 0xf, 0xf4, 0xf, 0xf3, 0xf, 0xf3,
    0x1, 0x10,

    /* U+0028 "(" */
    0x0, 0x0, 0xdf, 0xe0, 0x0, 0x5, 0xff, 0x60,
    0x0, 0xd, 0xfe, 0x0, 0x0, 0x4f, 0xf7, 0x0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0xff, 0xd0, 0x0,
    0x4, 0xff, 0x80, 0x0, 0x7, 0xff, 0x50, 0x0,
    0xa, 0xff, 0x20, 0x0, 0xd, 0xff, 0x0, 0x0,
    0xf, 0xfd, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0,
    0x1f, 0xfc, 0x0, 0x0, 0x2f, 0xfb, 0x0, 0x0,
    0x2f, 0xfb, 0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0xf, 0xfd, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0,
    0x7, 0xff, 0x50, 0x0, 0x4, 0xff, 0x80, 0x0,
    0x0, 0xef, 0xd0, 0x0, 0x0, 0xaf, 0xf2, 0x0,
    0x0, 0x4f, 0xf7, 0x0, 0x0, 0xd, 0xfe, 0x0,
    0x0, 0x5, 0xff, 0x60, 0x0, 0x0, 0xdf, 0xe0,

    /* U+0029 ")" */
    0xc, 0xfe, 0x0, 0x0, 0x5, 0xff, 0x70, 0x0,
    0x0, 0xdf, 0xf1, 0x0, 0x0, 0x6f, 0xf6, 0x0,
    0x0, 0x1f, 0xfc, 0x0, 0x0, 0xb, 0xff, 0x10,
    0x0, 0x6, 0xff, 0x60, 0x0, 0x3, 0xff, 0x90,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0x0, 0xbf, 0xf1, 0x0, 0x0, 0xaf, 0xf2,
    0x0, 0x0, 0x9f, 0xf3, 0x0, 0x0, 0x8f, 0xf4,
    0x0, 0x0, 0x9f, 0xf4, 0x0, 0x0, 0x9f, 0xf3,
    0x0, 0x0, 0xaf, 0xf2, 0x0, 0x0, 0xbf, 0xf1,
    0x0, 0x0, 0xdf, 0xf0, 0x0, 0x0, 0xff, 0xc0,
    0x0, 0x3, 0xff, 0x90, 0x0, 0x6, 0xff, 0x60,
    0x0, 0xb, 0xff, 0x10, 0x0, 0x1f, 0xfc, 0x0,
    0x0, 0x6f, 0xf6, 0x0, 0x0, 0xdf, 0xe1, 0x0,
    0x5, 0xff, 0x70, 0x0, 0xc, 0xfe, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xe, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xe0, 0x0, 0x0, 0xc, 0x70, 0xe, 0xe0,
    0x7, 0xc0, 0x3f, 0xfe, 0x5e, 0xe5, 0xef, 0xf3,
    0x2, 0xaf, 0xff, 0xff, 0xfa, 0x20, 0x0, 0x4,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf8, 0x0, 0x3e, 0xff, 0x7e, 0xe7, 0xff, 0xe3,
    0xd, 0xa1, 0xe, 0xe0, 0x1a, 0xd0, 0x0, 0x0,
    0xe, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x56, 0x66, 0x6b, 0xff, 0x66, 0x66, 0x62, 0x0,
    0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0,
    0x0, 0x0,

    /* U+002C "," */
    0x1, 0x41, 0x3, 0xff, 0xe1, 0x8f, 0xff, 0x55,
    0xff, 0xf4, 0x9, 0xff, 0x0, 0x8f, 0xa0, 0xc,
    0xf5, 0x0, 0xff, 0x0, 0x4f, 0xa0, 0x0,

    /* U+002D "-" */
    0x27, 0x77, 0x77, 0x77, 0x64, 0xff, 0xff, 0xff,
    0xfc, 0x4f, 0xff, 0xff, 0xff, 0xc0,

    /* U+002E "." */
    0x0, 0x0, 0x1, 0xdf, 0xb0, 0x8f, 0xff, 0x58,
    0xff, 0xf4, 0x1b, 0xf9, 0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xd9, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xfe, 0xbb, 0xef,
    0xff, 0xc0, 0x0, 0x0, 0xaf, 0xfe, 0x50, 0x0,
    0x5, 0xef, 0xfa, 0x0, 0x4, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x40, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0xf, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf0, 0x4f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4,
    0x6f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf6, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf8, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf8, 0x8f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf8, 0x6f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf6, 0x4f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0xf, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf0, 0xb,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0,
    0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x40, 0x0, 0xaf, 0xfe, 0x50, 0x0, 0x4, 0xef,
    0xfa, 0x0, 0x0, 0xc, 0xff, 0xfe, 0xbb, 0xef,
    0xff, 0xc0, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff,
    0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x3, 0x9d,
    0xff, 0xda, 0x30, 0x0, 0x0,

    /* U+0031 "1" */
    0xcf, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0x79, 0x99, 0x9f, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xf, 0xff,

    /* U+0032 "2" */
    0x0, 0x1, 0x6b, 0xdf, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x1c, 0xff, 0xff, 0xcb, 0xbd, 0xff, 0xff,
    0x30, 0x3, 0xef, 0xe5, 0x0, 0x0, 0x4, 0xdf,
    0xfd, 0x0, 0x2, 0xa1, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x10, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30,

    /* U+0033 "3" */
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0x99, 0x99, 0x99, 0x99, 0x9f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xfd, 0x80, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x11, 0x13, 0x8e, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x8, 0x30, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf7,
    0x4f, 0xfa, 0x30, 0x0, 0x0, 0x1a, 0xff, 0xf1,
    0x9f, 0xff, 0xfe, 0xca, 0xbd, 0xff, 0xff, 0x50,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x4, 0x8c, 0xef, 0xfd, 0xb6, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf9,
    0x0, 0x0, 0x33, 0x20, 0x0, 0x0, 0x0, 0x7f,
    0xfc, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0,
    0x3f, 0xfe, 0x20, 0x0, 0x1, 0xff, 0xb0, 0x0,
    0x0, 0x1e, 0xff, 0x40, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0xc, 0xff, 0x80, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x8, 0xff, 0xf9, 0x99, 0x99,
    0x99, 0x9f, 0xfe, 0x99, 0x96, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x1f, 0xfd, 0x99, 0x99, 0x99, 0x99,
    0x90, 0x0, 0x3, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xfe, 0xb8, 0x20, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x89, 0x99,
    0x9a, 0xbd, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x8f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x5, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfc,
    0x0, 0xef, 0xd5, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x60, 0x3f, 0xff, 0xff, 0xdb, 0xac, 0xff, 0xff,
    0xa0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x2, 0x7b, 0xdf, 0xfe, 0xc8,
    0x20, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x5a, 0xdf, 0xff, 0xc9, 0x40,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x8, 0xff, 0xff, 0xca, 0x99, 0xcf,
    0xe0, 0x0, 0x6, 0xff, 0xf9, 0x10, 0x0, 0x0,
    0x13, 0x0, 0x1, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0x1, 0x7c,
    0xef, 0xec, 0x71, 0x0, 0x8, 0xff, 0x75, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0x0, 0x8f, 0xfb, 0xff,
    0xea, 0x77, 0x9e, 0xff, 0xf5, 0x8, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x9, 0xff, 0xe0, 0x7f, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x65, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0x1f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x3, 0xff, 0xa0,
    0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x50, 0xd, 0xff, 0xa0, 0x0, 0x0, 0x9, 0xff,
    0xd0, 0x0, 0x2e, 0xff, 0xea, 0x77, 0x9e, 0xff,
    0xf3, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x0, 0x4, 0x9d, 0xff, 0xeb,
    0x60, 0x0, 0x0,

    /* U+0037 "7" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x1f, 0xfe, 0x99, 0x99, 0x99, 0x99, 0x9e,
    0xff, 0x81, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf1, 0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xfa, 0x1, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x20, 0x5, 0x54, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x39, 0xce, 0xfe, 0xda, 0x50, 0x0,
    0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0xdf, 0xff, 0xb8, 0x78, 0xaf, 0xff,
    0xf3, 0x0, 0x7f, 0xfe, 0x30, 0x0, 0x0, 0x1a,
    0xff, 0xc0, 0xc, 0xff, 0x40, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x10, 0xef, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf3, 0xc, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x10, 0x7f, 0xfc, 0x10, 0x0,
    0x0, 0x8, 0xff, 0xb0, 0x0, 0xbf, 0xff, 0x96,
    0x45, 0x8d, 0xff, 0xe2, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0x0, 0x0, 0x6e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x7f, 0xff,
    0x93, 0x10, 0x3, 0x7e, 0xff, 0xb0, 0x2f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x67, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc, 0xaf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfa,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x7f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xc1, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x7, 0xff,
    0xf5, 0x5, 0xff, 0xff, 0xa7, 0x67, 0x9e, 0xff,
    0xf9, 0x0, 0x4, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x59, 0xde, 0xfe, 0xdb,
    0x61, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x38, 0xde, 0xfe, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xb7, 0x78, 0xcf,
    0xff, 0x80, 0x0, 0x6, 0xff, 0xd2, 0x0, 0x0,
    0x3, 0xdf, 0xf5, 0x0, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x2f, 0xfe, 0x0, 0xf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x1f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x90, 0xf,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0,
    0xb, 0xff, 0x90, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf0, 0x3, 0xff, 0xfb, 0x41, 0x1, 0x5c, 0xff,
    0xff, 0xf0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xf0, 0x0, 0x3, 0xbf, 0xff, 0xff,
    0xfb, 0x30, 0xff, 0xf0, 0x0, 0x0, 0x1, 0x46,
    0x64, 0x10, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xf9,
    0x0, 0x0, 0x5, 0x0, 0x0, 0x0, 0x5e, 0xff,
    0xd0, 0x0, 0x0, 0x7f, 0xeb, 0xa9, 0xae, 0xff,
    0xfd, 0x20, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x17, 0xbe, 0xff,
    0xec, 0x82, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x1b, 0xfa, 0x8, 0xff, 0xf4, 0x8f, 0xff, 0x51,
    0xdf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfb,
    0x8, 0xff, 0xf5, 0x8f, 0xff, 0x41, 0xbf, 0x90,

    /* U+003B ";" */
    0x1b, 0xfa, 0x8, 0xff, 0xf4, 0x8f, 0xff, 0x51,
    0xdf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfa,
    0x7, 0xff, 0xf4, 0x7f, 0xff, 0x51, 0xcf, 0xf1,
    0x7, 0xfc, 0x0, 0xbf, 0x70, 0xf, 0xf1, 0x3,
    0xfc, 0x0, 0x25, 0x20, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xa6, 0x0, 0x0,
    0x0, 0x0, 0x17, 0xdf, 0xf7, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0xc3, 0x0, 0x2, 0x8e, 0xff,
    0xfe, 0x93, 0x0, 0x5, 0xbf, 0xff, 0xfb, 0x50,
    0x0, 0x0, 0xef, 0xff, 0xd7, 0x10, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xd6, 0x10, 0x0, 0x0, 0x0, 0x6,
    0xcf, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x3,
    0x9f, 0xff, 0xfe, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xb7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003D "=" */
    0x56, 0x66, 0x66, 0x66, 0x66, 0x66, 0x62, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x52,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd7,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfa,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfd,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x28, 0xef, 0xff,
    0xe8, 0x20, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xf7,
    0x0, 0x0, 0x0, 0x3, 0xaf, 0xff, 0xf6, 0x0,
    0x0, 0x17, 0xdf, 0xff, 0xf9, 0x30, 0x0, 0x5b,
    0xff, 0xff, 0xc5, 0x0, 0x0, 0x8e, 0xff, 0xfe,
    0x82, 0x0, 0x0, 0x0, 0xff, 0xfb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0x2, 0x7b, 0xdf, 0xfe, 0xb7, 0x10, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x1d, 0xff, 0xfe, 0xa9, 0x9c, 0xff, 0xff, 0x40,
    0x6f, 0xfd, 0x40, 0x0, 0x0, 0x2d, 0xff, 0xd0,
    0x3, 0xa0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x43, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xf9, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xdf, 0xff,
    0xeb, 0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xcf, 0xfe, 0x94, 0x20, 0x0, 0x14, 0x7c, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf, 0xf8,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0x0,
    0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x2, 0x44,
    0x20, 0x0, 0x25, 0x50, 0x5f, 0xf4, 0x0, 0x0,
    0xcf, 0xc0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0xd6,
    0x9, 0xff, 0x0, 0x7f, 0xe0, 0x0, 0x4f, 0xf2,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xfb, 0x9f,
    0xf0, 0x0, 0xcf, 0x70, 0xb, 0xfa, 0x0, 0x0,
    0xcf, 0xfc, 0x51, 0x2, 0x7e, 0xff, 0xff, 0x0,
    0x5, 0xfd, 0x0, 0xff, 0x40, 0x0, 0x7f, 0xfa,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xf0, 0x0, 0xe,
    0xf2, 0x4f, 0xf0, 0x0, 0xe, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x0, 0x0, 0xbf, 0x56,
    0xfc, 0x0, 0x3, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf0, 0x0, 0x8, 0xf7, 0x8f, 0xa0,
    0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0x0, 0x7f, 0x88, 0xfa, 0x0, 0x6,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0,
    0x0, 0x7, 0xf8, 0x8f, 0xa0, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x0, 0x0,
    0x8f, 0x76, 0xfc, 0x0, 0x1, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf0, 0x0, 0xa, 0xf5,
    0x3f, 0xf0, 0x0, 0xc, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x0, 0x0, 0xef, 0x20, 0xff,
    0x40, 0x0, 0x4f, 0xfd, 0x20, 0x0, 0x0, 0x5f,
    0xff, 0xf3, 0x0, 0x6f, 0xd0, 0xa, 0xfa, 0x0,
    0x0, 0x9f, 0xff, 0x95, 0x46, 0xbf, 0xf9, 0xff,
    0xc5, 0x7f, 0xf5, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xf6, 0xc, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0xbf, 0xc0, 0x0, 0x0, 0x29,
    0xdf, 0xfd, 0x82, 0x0, 0x19, 0xef, 0xd6, 0x0,
    0x0, 0x2, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf,
    0xfe, 0x95, 0x21, 0x1, 0x36, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xef,
    0xff, 0xdb, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xcd, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x1,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x70, 0x9, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf1, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xfa, 0x0, 0x0, 0xbf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x30, 0x0, 0x4, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xc0,
    0x0, 0x0, 0xd, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x6f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xb6, 0x66, 0x66, 0x66, 0x6c, 0xff, 0x90,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xe0, 0x0, 0x7, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x60, 0x0,
    0xef, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfd, 0x0, 0x5f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf4, 0xc, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xc0,

    /* U+0042 "B" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xa6, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x40, 0x0, 0xdf, 0xf8, 0x66, 0x66, 0x66,
    0x8b, 0xff, 0xff, 0x30, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xfb, 0x0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf9, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x14, 0xaf, 0xfe, 0x10, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe7, 0x0,
    0xd, 0xff, 0x86, 0x66, 0x66, 0x67, 0x8b, 0xff,
    0xfb, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xf8, 0xd, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf0, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x2d, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x1d, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xb0, 0xdf, 0xf8, 0x66, 0x66, 0x66,
    0x78, 0xbf, 0xff, 0xf2, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd3, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xda, 0x50, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x5, 0x9d, 0xff, 0xed, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xe6, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xfe, 0xba, 0xbd, 0xff, 0xff, 0xa0, 0x0, 0x1d,
    0xff, 0xfb, 0x30, 0x0, 0x0, 0x29, 0xff, 0xf3,
    0x0, 0xbf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0x40, 0x5, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0x40, 0x0, 0x1d, 0xff, 0xfa, 0x30, 0x0,
    0x0, 0x29, 0xff, 0xf3, 0x0, 0x1, 0xbf, 0xff,
    0xfe, 0xba, 0xbd, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xad, 0xff, 0xed, 0x95,
    0x0, 0x0,

    /* U+0044 "D" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xec, 0x94, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0x0, 0x0, 0xdf, 0xfa, 0x99,
    0x99, 0x9a, 0xce, 0xff, 0xff, 0xa0, 0x0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xff,
    0xb0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x80, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x20, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf9, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xe0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x2d, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf4, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x5d, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf4, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x2d, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xe0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf9, 0xd, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x20,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x80, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xff, 0xb0, 0x0, 0xdf, 0xfa, 0x99,
    0x99, 0x9a, 0xce, 0xff, 0xff, 0xa0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x50,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xec,
    0x94, 0x0, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xdf, 0xfa, 0x99, 0x99, 0x99, 0x99, 0x99, 0x90,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xdf, 0xf9, 0x88, 0x88, 0x88, 0x88, 0x86, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xfa, 0x99, 0x99, 0x99, 0x99, 0x99, 0x93,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+0046 "F" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xfa, 0x99, 0x99, 0x99, 0x99, 0x99, 0x9d, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0xdf, 0xfa, 0x99, 0x99, 0x99, 0x99,
    0x96, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xed, 0xa6,
    0x10, 0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xfe, 0xca, 0xbd, 0xff, 0xff, 0xd1, 0x0, 0xc,
    0xff, 0xfb, 0x30, 0x0, 0x0, 0x17, 0xef, 0xf6,
    0x0, 0xaf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1b, 0x70, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2a, 0xa5, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x2f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf9, 0xc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x4, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0x0, 0xaf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0xc, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x5, 0xdf, 0xf9, 0x0, 0x1, 0xbf, 0xff,
    0xfe, 0xba, 0xbc, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x10,
    0x0, 0x0, 0x0, 0x5, 0x9d, 0xff, 0xed, 0xa6,
    0x10, 0x0,

    /* U+0048 "H" */
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x3d, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf3, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x3d, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x3d,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf3, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x3d, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x3d, 0xff, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0xef, 0xf3, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x3d, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf3, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x3d, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x3d, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf3, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x3d, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x30,

    /* U+0049 "I" */
    0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2,
    0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2,
    0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2,
    0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2,
    0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2, 0xdf, 0xf2,
    0xdf, 0xf2,

    /* U+004A "J" */
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x19,
    0x99, 0x99, 0x99, 0xdf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf5,
    0x1, 0xb1, 0x0, 0x0, 0x1, 0xff, 0xf2, 0xc,
    0xfd, 0x20, 0x0, 0xa, 0xff, 0xd0, 0xc, 0xff,
    0xfc, 0x99, 0xef, 0xff, 0x50, 0x1, 0xaf, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x4, 0xad, 0xff,
    0xd9, 0x20, 0x0,

    /* U+004B "K" */
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xf7, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xf8, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xf9, 0x0, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0xbf, 0xfc, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x20, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0xaf,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0xd, 0xff, 0x20,
    0xaf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf2, 0xaf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xbf, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0x35, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x30, 0x7,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x30,
    0x0, 0x9, 0xff, 0xf3, 0x0, 0x0, 0xd, 0xff,
    0x40, 0x0, 0x0, 0xb, 0xff, 0xe1, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xa0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x70, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x20,

    /* U+004C "L" */
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfa, 0x99, 0x99, 0x99, 0x99, 0x99, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+004D "M" */
    0xdf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x7d, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf7, 0xdf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x7d, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf7, 0xdf, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x7d, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf7, 0xdf, 0xf9, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf8, 0xff,
    0x7d, 0xff, 0x1e, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x6f, 0xf8, 0x6f, 0xf7, 0xdf, 0xf0, 0x5f, 0xfa,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0x6, 0xff, 0x7d,
    0xff, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x8, 0xff,
    0x50, 0x6f, 0xf7, 0xdf, 0xf0, 0x2, 0xff, 0xd0,
    0x0, 0x2, 0xff, 0xb0, 0x6, 0xff, 0x7d, 0xff,
    0x0, 0x8, 0xff, 0x60, 0x0, 0xbf, 0xf2, 0x0,
    0x6f, 0xf7, 0xdf, 0xf0, 0x0, 0xe, 0xff, 0x10,
    0x4f, 0xf8, 0x0, 0x6, 0xff, 0x7d, 0xff, 0x0,
    0x0, 0x5f, 0xf9, 0xd, 0xfe, 0x0, 0x0, 0x6f,
    0xf7, 0xdf, 0xf0, 0x0, 0x0, 0xbf, 0xfa, 0xff,
    0x50, 0x0, 0x6, 0xff, 0x7d, 0xff, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x6f, 0xf7,
    0xdf, 0xf0, 0x0, 0x0, 0x8, 0xff, 0xf2, 0x0,
    0x0, 0x6, 0xff, 0x7d, 0xff, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x37, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x7d, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0xdf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x70,

    /* U+004E "N" */
    0xdf, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x3d, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf3, 0xdf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x3d, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3, 0xdf, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0xc, 0xff, 0x3d,
    0xff, 0xdf, 0xfe, 0x10, 0x0, 0x0, 0x0, 0xcf,
    0xf3, 0xdf, 0xf3, 0xdf, 0xfc, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x3d, 0xff, 0x22, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0xdf, 0xf2, 0x5, 0xff,
    0xf7, 0x0, 0x0, 0xc, 0xff, 0x3d, 0xff, 0x20,
    0x8, 0xff, 0xf4, 0x0, 0x0, 0xcf, 0xf3, 0xdf,
    0xf2, 0x0, 0xb, 0xff, 0xe2, 0x0, 0xc, 0xff,
    0x3d, 0xff, 0x20, 0x0, 0xd, 0xff, 0xd0, 0x0,
    0xcf, 0xf3, 0xdf, 0xf2, 0x0, 0x0, 0x2f, 0xff,
    0xa0, 0xc, 0xff, 0x3d, 0xff, 0x20, 0x0, 0x0,
    0x4f, 0xff, 0x70, 0xcf, 0xf3, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x4c, 0xff, 0x3d, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0xef, 0xf3,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0x3d, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xf3, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x3d, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfd, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xfe, 0xba, 0xbd, 0xff, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xa3, 0x0,
    0x0, 0x2, 0x8f, 0xff, 0xe2, 0x0, 0x0, 0xaf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xd0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x80, 0xc, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x2, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf5, 0x5f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x98,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfb, 0x8f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfb, 0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x92, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf5, 0xc, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x0, 0x4f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x80,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0xff, 0xa3,
    0x0, 0x0, 0x2, 0x8f, 0xff, 0xe2, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xfe, 0xba, 0xbd, 0xff, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9d, 0xff, 0xfd, 0xa6, 0x10, 0x0,
    0x0, 0x0,

    /* U+0050 "P" */
    0xdf, 0xff, 0xff, 0xff, 0xfe, 0xd9, 0x40, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0xdf, 0xfa, 0x99, 0x99, 0x9a, 0xdf,
    0xff, 0xf4, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x3, 0xcf, 0xff, 0x10, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x90, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xc0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0x60, 0xdf, 0xf2, 0x0, 0x0, 0x1,
    0x49, 0xff, 0xfc, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0x0, 0x0, 0xdf, 0xfa,
    0x99, 0x99, 0x98, 0x63, 0x0, 0x0, 0x0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfd, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xfe, 0xba, 0xbd, 0xff,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfa,
    0x30, 0x0, 0x0, 0x29, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xd0, 0x0, 0x4, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x0,
    0xc, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfe, 0x0, 0x1f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x50,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x80, 0x7f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb0,
    0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xb0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0,
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x90, 0x2f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50,
    0xd, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x0, 0x6, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf8, 0x0,
    0x0, 0xcf, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xd0, 0x0, 0x0, 0x1e, 0xff, 0xf9,
    0x20, 0x0, 0x0, 0x17, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x2, 0xdf, 0xff, 0xfd, 0xa9, 0xac, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0xbe, 0xff, 0xff, 0xe6,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf4, 0x0, 0x0, 0x2, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x92, 0x2, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x54, 0x0, 0x0,

    /* U+0052 "R" */
    0xdf, 0xff, 0xff, 0xff, 0xfe, 0xd9, 0x40, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0xdf, 0xfa, 0x99, 0x99, 0x9a, 0xdf,
    0xff, 0xf4, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x3, 0xcf, 0xff, 0x10, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x90, 0xdf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0xdf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xc0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0xdf, 0xf2, 0x0, 0x0, 0x1,
    0x49, 0xff, 0xfc, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0xdf, 0xf9,
    0x88, 0x88, 0x89, 0xff, 0xd0, 0x0, 0x0, 0xdf,
    0xf2, 0x0, 0x0, 0x0, 0xaf, 0xf9, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xe1, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfb, 0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x60, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf2,

    /* U+0053 "S" */
    0x0, 0x0, 0x38, 0xce, 0xff, 0xeb, 0x72, 0x0,
    0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x1, 0xef, 0xff, 0xc9, 0x89, 0xbe, 0xff,
    0xf4, 0x0, 0xaf, 0xfc, 0x20, 0x0, 0x0, 0x3,
    0xad, 0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfc, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xfd, 0x95, 0x10, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xae, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x7c, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf5,
    0x9, 0x20, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x35, 0xff, 0x92, 0x0, 0x0, 0x0, 0x1a, 0xff,
    0xc0, 0x7f, 0xff, 0xfe, 0xb9, 0x89, 0xcf, 0xff,
    0xf3, 0x0, 0x4c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc3, 0x0, 0x0, 0x3, 0x7b, 0xdf, 0xfe, 0xd9,
    0x40, 0x0, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x89, 0x99, 0x99, 0x9d, 0xff, 0xb9,
    0x99, 0x99, 0x94, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x40, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xb0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfb, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xb0, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0xf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xb0,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfb, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xb0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfb, 0xf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0xf,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xb0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfb, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xa0, 0xef, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf9, 0xb, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x70, 0x7f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0x1, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfc, 0x0, 0x8, 0xff, 0xf7, 0x0, 0x0, 0x2,
    0xaf, 0xff, 0x40, 0x0, 0xb, 0xff, 0xff, 0xca,
    0xbd, 0xff, 0xff, 0x70, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0,
    0x2, 0x8c, 0xef, 0xfe, 0xa6, 0x0, 0x0, 0x0,

    /* U+0056 "V" */
    0xc, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x20, 0x5f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0xef, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf4, 0x0, 0x7, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x1f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x60, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x2, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x70, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xfe,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0xcf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc0,
    0x0, 0x0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x30, 0x0, 0xb, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0,
    0x2, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf2, 0x0, 0x9f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x80, 0x1f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf7, 0xef, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0057 "W" */
    0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xa0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf4, 0x3, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0xd, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf4, 0x0, 0x3, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x4f, 0xf7, 0xaf, 0xf3, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x0, 0x0, 0xd, 0xff, 0x30, 0x0,
    0x0, 0xa, 0xff, 0x24, 0xff, 0x90, 0x0, 0x0,
    0x3, 0xff, 0xa0, 0x0, 0x0, 0x8f, 0xf8, 0x0,
    0x0, 0x0, 0xff, 0xc0, 0xe, 0xfe, 0x0, 0x0,
    0x0, 0x8f, 0xf4, 0x0, 0x0, 0x3, 0xff, 0xd0,
    0x0, 0x0, 0x5f, 0xf7, 0x0, 0x9f, 0xf4, 0x0,
    0x0, 0xe, 0xff, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x30, 0x0, 0xa, 0xff, 0x10, 0x4, 0xff, 0x90,
    0x0, 0x3, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x8f,
    0xf8, 0x0, 0x0, 0xff, 0xc0, 0x0, 0xe, 0xfe,
    0x0, 0x0, 0x9f, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xd0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x9f,
    0xf4, 0x0, 0xe, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x30, 0xb, 0xff, 0x10, 0x0, 0x4,
    0xff, 0x90, 0x3, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf8, 0x1, 0xff, 0xc0, 0x0, 0x0,
    0xe, 0xfe, 0x0, 0x9f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xd0, 0x6f, 0xf6, 0x0, 0x0,
    0x0, 0x9f, 0xf4, 0xe, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x3b, 0xff, 0x10, 0x0,
    0x0, 0x3, 0xff, 0xa4, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfa, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xe, 0xfe, 0x9f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0,
    0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xd0, 0x1, 0xef, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x30, 0x0, 0x5f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0xcf, 0xf6, 0x0, 0x0, 0x9,
    0xff, 0xd0, 0x0, 0x0, 0x8, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x4f, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x40, 0x1,
    0xef, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xe1, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x6f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xfd, 0xcf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf3, 0x2f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x70, 0x6, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfc, 0x0, 0x0, 0xaf, 0xfc, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf1, 0x0, 0x0, 0xd, 0xff,
    0x80, 0x0, 0x0, 0x1e, 0xff, 0x50, 0x0, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0xcf, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfe, 0x10, 0x8, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0,
    0x4f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xf6,

    /* U+0059 "Y" */
    0xc, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x30, 0x3f, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x9f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe1, 0x0,
    0x1, 0xef, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf6, 0x0, 0x0, 0x6, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x60, 0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x10, 0x0, 0x6, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0, 0x1,
    0xef, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf4, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xd0, 0x3f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x7d, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x49, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x9d, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xc9, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x98, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+005B "[" */
    0xdf, 0xff, 0xff, 0x6d, 0xff, 0xff, 0xf6, 0xdf,
    0xf6, 0x55, 0x2d, 0xff, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0, 0x0,
    0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0, 0x0, 0xdf,
    0xf0, 0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd,
    0xff, 0x65, 0x52, 0xdf, 0xff, 0xff, 0x6d, 0xff,
    0xff, 0xf6,

    /* U+005C "\\" */
    0xe, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf3,

    /* U+005D "]" */
    0x7f, 0xff, 0xff, 0xd7, 0xff, 0xff, 0xfd, 0x25,
    0x56, 0xff, 0xd0, 0x0, 0xf, 0xfd, 0x0, 0x0,
    0xff, 0xd0, 0x0, 0xf, 0xfd, 0x0, 0x0, 0xff,
    0xd0, 0x0, 0xf, 0xfd, 0x0, 0x0, 0xff, 0xd0,
    0x0, 0xf, 0xfd, 0x0, 0x0, 0xff, 0xd0, 0x0,
    0xf, 0xfd, 0x0, 0x0, 0xff, 0xd0, 0x0, 0xf,
    0xfd, 0x0, 0x0, 0xff, 0xd0, 0x0, 0xf, 0xfd,
    0x0, 0x0, 0xff, 0xd0, 0x0, 0xf, 0xfd, 0x0,
    0x0, 0xff, 0xd0, 0x0, 0xf, 0xfd, 0x0, 0x0,
    0xff, 0xd0, 0x0, 0xf, 0xfd, 0x0, 0x0, 0xff,
    0xd0, 0x0, 0xf, 0xfd, 0x0, 0x0, 0xff, 0xd2,
    0x55, 0x6f, 0xfd, 0x7f, 0xff, 0xff, 0xd7, 0xff,
    0xff, 0xfd,

    /* U+005E "^" */
    0x0, 0x0, 0x4, 0x87, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x8f, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xff, 0x19,
    0xfa, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x2, 0xff,
    0x10, 0x0, 0x0, 0xf, 0xf4, 0x0, 0xbf, 0x70,
    0x0, 0x0, 0x6f, 0xd0, 0x0, 0x5f, 0xe0, 0x0,
    0x0, 0xdf, 0x60, 0x0, 0xe, 0xf5, 0x0, 0x4,
    0xff, 0x0, 0x0, 0x8, 0xfc, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0x1, 0xff, 0x20, 0x2f, 0xf2, 0x0,
    0x0, 0x0, 0xaf, 0x90, 0x8f, 0xc0, 0x0, 0x0,
    0x0, 0x4f, 0xf1,

    /* U+005F "_" */
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x1c, 0xff, 0xa0, 0x0, 0x0, 0x9, 0xff, 0xa0,
    0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0, 0x3,
    0xef, 0xb0,

    /* U+0061 "a" */
    0x0, 0x4, 0x9d, 0xef, 0xec, 0x82, 0x0, 0x0,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0xc,
    0xff, 0xea, 0x88, 0x9e, 0xff, 0xf4, 0x0, 0x3e,
    0x50, 0x0, 0x0, 0xa, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x19, 0xff, 0x50, 0x2, 0x9d, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x51, 0xff, 0xf8, 0x20, 0x0,
    0x0, 0x8f, 0xf5, 0x6f, 0xf9, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x57, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xbf, 0xf5, 0x5f, 0xfa, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x50, 0xef, 0xf9, 0x31, 0x14, 0xaf, 0xff,
    0xf5, 0x3, 0xef, 0xff, 0xff, 0xff, 0xd8, 0xff,
    0x50, 0x1, 0x8c, 0xff, 0xec, 0x70, 0x6f, 0xf5,

    /* U+0062 "b" */
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0x1, 0x8c, 0xef, 0xea, 0x50, 0x0, 0x0, 0x4f,
    0xf9, 0x5f, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0x4f, 0xfd, 0xff, 0xfb, 0x99, 0xcf, 0xff, 0xf2,
    0x0, 0x4f, 0xff, 0xfb, 0x10, 0x0, 0x2, 0xcf,
    0xfd, 0x0, 0x4f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x70, 0x4f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xd0, 0x4f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2, 0x4f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2, 0x4f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0x4f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xd0, 0x4f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x70, 0x4f, 0xff, 0xfb, 0x10, 0x0, 0x2,
    0xcf, 0xfd, 0x0, 0x4f, 0xfc, 0xff, 0xfb, 0x89,
    0xbf, 0xff, 0xf2, 0x0, 0x4f, 0xf7, 0x5f, 0xff,
    0xff, 0xff, 0xfd, 0x20, 0x0, 0x4f, 0xf7, 0x1,
    0x8c, 0xff, 0xda, 0x40, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xc7, 0x10, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x8f, 0xff, 0xea, 0x89, 0xdf, 0xff, 0x70,
    0x5, 0xff, 0xf7, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x46, 0x0,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x50, 0x0, 0x0, 0x0, 0x46, 0x0,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0x0, 0x8f, 0xff, 0xea, 0x89, 0xdf, 0xff, 0x70,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xc8, 0x20, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfb, 0x0, 0x0, 0x28, 0xdf, 0xfe,
    0xa4, 0x2, 0xff, 0xb0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfb, 0x3f, 0xfb, 0x0, 0xaf, 0xff, 0xea,
    0x8a, 0xef, 0xfd, 0xff, 0xb0, 0x7f, 0xff, 0x70,
    0x0, 0x0, 0x6f, 0xff, 0xfb, 0x1f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xb6, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x9f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xbb, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0xbf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xb9,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xb1, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfb, 0x7, 0xff, 0xe4, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xb0, 0xa, 0xff, 0xfb, 0x75, 0x7b, 0xff,
    0xdf, 0xfb, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xc2, 0xff, 0xb0, 0x0, 0x2, 0x8c, 0xef, 0xea,
    0x50, 0xf, 0xfb,

    /* U+0065 "e" */
    0x0, 0x0, 0x28, 0xdf, 0xfe, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0xaf, 0xff, 0xb7, 0x79, 0xef, 0xfe,
    0x20, 0x0, 0x6f, 0xfd, 0x20, 0x0, 0x0, 0x8f,
    0xfc, 0x0, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x9f, 0xf5, 0x5, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x9f, 0xf4, 0x11, 0x11, 0x11,
    0x11, 0x1c, 0xfe, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x19, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x8f, 0x40, 0x0, 0x9,
    0xff, 0xfe, 0xa8, 0x8b, 0xff, 0xfd, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0x0, 0x1, 0x7b, 0xef, 0xfd, 0x94, 0x0, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x5, 0xbe, 0xfd, 0x92, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xf6, 0x0, 0x5, 0xff, 0xf8,
    0x68, 0xc0, 0x0, 0xb, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x36, 0x6e, 0xff, 0x66, 0x66, 0x30, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x28, 0xce, 0xfe, 0xb5, 0x0, 0xcf,
    0xf0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfd, 0x2c,
    0xff, 0x0, 0xbf, 0xff, 0xea, 0x89, 0xcf, 0xfe,
    0xef, 0xf0, 0x8f, 0xff, 0x60, 0x0, 0x0, 0x3d,
    0xff, 0xff, 0x1f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xf6, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xaf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfb, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xbf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf8, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x4f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf0, 0xdf, 0xfb,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x2, 0xff,
    0xfd, 0x62, 0x1, 0x4b, 0xff, 0xff, 0xf0, 0x4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xfe, 0x0,
    0x1, 0xaf, 0xff, 0xff, 0xfe, 0x50, 0xff, 0xe0,
    0x0, 0x0, 0x4, 0x67, 0x63, 0x0, 0xf, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xa0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf5, 0x2, 0xfd, 0x50, 0x0, 0x0, 0x1, 0xaf,
    0xfe, 0x0, 0xaf, 0xff, 0xfb, 0x98, 0x9b, 0xff,
    0xff, 0x40, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x16, 0xad, 0xef, 0xfd,
    0xa6, 0x0, 0x0,

    /* U+0068 "h" */
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x2, 0x8d, 0xef, 0xea, 0x40, 0x0,
    0x4f, 0xf9, 0x7f, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x4f, 0xfe, 0xff, 0xea, 0x9a, 0xef, 0xff, 0x90,
    0x4f, 0xff, 0xf8, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0x4f, 0xff, 0x80, 0x0, 0x0, 0x0, 0xaf, 0xf8,
    0x4f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfc,
    0x4f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,

    /* U+0069 "i" */
    0x2d, 0xe7, 0xb, 0xff, 0xf0, 0x9f, 0xfe, 0x1,
    0x9a, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf9, 0x4, 0xff, 0x90, 0x4f, 0xf9, 0x4, 0xff,
    0x90, 0x4f, 0xf9, 0x4, 0xff, 0x90, 0x4f, 0xf9,
    0x4, 0xff, 0x90, 0x4f, 0xf9, 0x4, 0xff, 0x90,
    0x4f, 0xf9, 0x4, 0xff, 0x90, 0x4f, 0xf9, 0x4,
    0xff, 0x90, 0x4f, 0xf9, 0x4, 0xff, 0x90,

    /* U+006A "j" */
    0x0, 0x0, 0x1, 0xcf, 0x90, 0x0, 0x0, 0x8,
    0xff, 0xf3, 0x0, 0x0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x8a, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xc0,
    0x0, 0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x2,
    0xff, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0x0,
    0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xff,
    0xc0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0x0, 0x0,
    0x2, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xc0,
    0x0, 0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x2,
    0xff, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0x0,
    0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xff,
    0xc0, 0x0, 0x0, 0x2, 0xff, 0xc0, 0x0, 0x0,
    0x2, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xff, 0xb0,
    0x0, 0x0, 0x8, 0xff, 0x80, 0x1e, 0x97, 0xaf,
    0xff, 0x20, 0x8f, 0xff, 0xff, 0xf7, 0x0, 0x3a,
    0xdf, 0xfb, 0x40, 0x0,

    /* U+006B "k" */
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf4, 0x4, 0xff, 0x90, 0x0, 0x0,
    0x5, 0xff, 0xf4, 0x0, 0x4f, 0xf9, 0x0, 0x0,
    0x7, 0xff, 0xf4, 0x0, 0x4, 0xff, 0x90, 0x0,
    0x8, 0xff, 0xf4, 0x0, 0x0, 0x4f, 0xf9, 0x0,
    0x9, 0xff, 0xf3, 0x0, 0x0, 0x4, 0xff, 0x90,
    0xa, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0xb, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xbc, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xdf, 0xff, 0x80, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xd1, 0x5f, 0xff, 0x50, 0x0, 0x0,
    0x4f, 0xff, 0xc0, 0x0, 0x8f, 0xff, 0x20, 0x0,
    0x4, 0xff, 0xc0, 0x0, 0x0, 0xbf, 0xfd, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0x0, 0x1, 0xdf, 0xfa,
    0x0, 0x4, 0xff, 0x90, 0x0, 0x0, 0x2, 0xff,
    0xf7, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf4, 0x4, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe1,

    /* U+006C "l" */
    0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9,
    0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9,
    0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9,
    0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9,
    0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9, 0x4f, 0xf9,
    0x4f, 0xf9, 0x4f, 0xf9,

    /* U+006D "m" */
    0x4f, 0xf7, 0x3, 0x9d, 0xff, 0xd8, 0x20, 0x0,
    0x6, 0xbe, 0xfe, 0xc7, 0x0, 0x0, 0x4f, 0xf7,
    0x9f, 0xff, 0xff, 0xff, 0xf6, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x4f, 0xfe, 0xff, 0xc8,
    0x78, 0xef, 0xff, 0x8f, 0xff, 0xa7, 0x79, 0xff,
    0xfe, 0x10, 0x4f, 0xff, 0xf5, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x1d, 0xff, 0x80,
    0x4f, 0xff, 0x60, 0x0, 0x0, 0x1, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x4f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf0, 0x4f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf1, 0x4f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2, 0x4f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf2, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf2, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2, 0x4f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf2, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf2, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,

    /* U+006E "n" */
    0x4f, 0xf7, 0x3, 0x9d, 0xef, 0xea, 0x40, 0x0,
    0x4f, 0xf7, 0x9f, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x4f, 0xfe, 0xff, 0xd8, 0x77, 0xcf, 0xff, 0x90,
    0x4f, 0xff, 0xf6, 0x0, 0x0, 0x5, 0xff, 0xf2,
    0x4f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x9f, 0xf8,
    0x4f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb,
    0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfc,
    0x4f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,

    /* U+006F "o" */
    0x0, 0x0, 0x17, 0xce, 0xff, 0xc8, 0x10, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x9f, 0xff, 0xea, 0x8a, 0xef, 0xff,
    0xa0, 0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x6f,
    0xff, 0x60, 0xe, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x15, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf6, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xab, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfc, 0xbf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xc9, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfa, 0x5f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x60, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf1, 0x5, 0xff,
    0xf6, 0x0, 0x0, 0x5, 0xff, 0xf6, 0x0, 0x9,
    0xff, 0xfe, 0x98, 0x9d, 0xff, 0xf9, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x1, 0x7c, 0xef, 0xfc, 0x81, 0x0, 0x0,

    /* U+0070 "p" */
    0x4f, 0xf7, 0x2, 0x8d, 0xef, 0xea, 0x50, 0x0,
    0x0, 0x4f, 0xf7, 0x7f, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x0, 0x4f, 0xfd, 0xff, 0xe9, 0x67, 0x9f,
    0xff, 0xf2, 0x0, 0x4f, 0xff, 0xf9, 0x0, 0x0,
    0x1, 0xbf, 0xfd, 0x0, 0x4f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x70, 0x4f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xd0, 0x4f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,
    0x4f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf2, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf1, 0x4f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xd0, 0x4f, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x70, 0x4f, 0xff, 0xfb, 0x10,
    0x0, 0x2, 0xdf, 0xfd, 0x0, 0x4f, 0xfd, 0xff,
    0xfb, 0x89, 0xbf, 0xff, 0xf2, 0x0, 0x4f, 0xf9,
    0x5e, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0, 0x4f,
    0xf9, 0x1, 0x8c, 0xef, 0xda, 0x40, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x28, 0xdf, 0xfe, 0xa4, 0x0, 0xff,
    0xb0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfb, 0x1f,
    0xfb, 0x0, 0xaf, 0xff, 0xea, 0x8a, 0xef, 0xfc,
    0xff, 0xb0, 0x7f, 0xff, 0x60, 0x0, 0x0, 0x6f,
    0xff, 0xfb, 0x1f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xb6, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x9f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xbb, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfb, 0xbf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xb9, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfb, 0x6f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xb1, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xfb, 0x7, 0xff,
    0xf6, 0x0, 0x0, 0x6, 0xff, 0xff, 0xb0, 0xa,
    0xff, 0xfe, 0x98, 0x9d, 0xff, 0xdf, 0xfb, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xa3, 0xff, 0xb0,
    0x0, 0x2, 0x8c, 0xef, 0xea, 0x40, 0x2f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfb,

    /* U+0072 "r" */
    0x4f, 0xf7, 0x2, 0x8d, 0xe4, 0x4f, 0xf7, 0x6f,
    0xff, 0xf4, 0x4f, 0xfc, 0xff, 0xfd, 0xc3, 0x4f,
    0xff, 0xfb, 0x20, 0x0, 0x4f, 0xff, 0xa0, 0x0,
    0x0, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x4f, 0xfc,
    0x0, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0,
    0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x4f,
    0xf9, 0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x4f, 0xf9,
    0x0, 0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x1, 0x7b, 0xef, 0xfe, 0xb7, 0x20, 0x0,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xff, 0x60, 0x2,
    0xff, 0xfd, 0x97, 0x79, 0xdf, 0xf1, 0x0, 0x9f,
    0xf9, 0x0, 0x0, 0x0, 0x35, 0x0, 0xb, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0x63, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xc9, 0x40, 0x0, 0x0, 0x3, 0xae, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x1, 0x47,
    0xbf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf1, 0x6, 0xc4, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x0, 0xef, 0xfe, 0xa8, 0x78, 0xbf, 0xff,
    0x80, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x1, 0x7b, 0xdf, 0xfe, 0xc9, 0x30, 0x0,

    /* U+0074 "t" */
    0x0, 0x6, 0x88, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x36, 0x6e, 0xff, 0x66,
    0x66, 0x30, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x10, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x70, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xfa, 0x79, 0xe1, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x6, 0xcf, 0xfd, 0x81,

    /* U+0075 "u" */
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x5f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf8,
    0x1f, 0xfe, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf8,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x2d, 0xff, 0xf8,
    0x3, 0xff, 0xfe, 0x86, 0x7a, 0xff, 0xef, 0xf8,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xfc, 0x4f, 0xf8,
    0x0, 0x1, 0x8c, 0xff, 0xeb, 0x50, 0x3f, 0xf8,

    /* U+0076 "v" */
    0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf9, 0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf2, 0x0, 0xef, 0xf0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x8f, 0xf6, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x40, 0x0, 0x1f, 0xfd, 0x0,
    0x0, 0x0, 0xe, 0xfd, 0x0, 0x0, 0xa, 0xff,
    0x40, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x3,
    0xff, 0xb0, 0x0, 0x0, 0xdf, 0xe0, 0x0, 0x0,
    0x0, 0xcf, 0xf2, 0x0, 0x4, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x5f, 0xf8, 0x0, 0xb, 0xff, 0x10,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x0, 0x2f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60, 0x9f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd1,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfb, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf1, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x94, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x5f, 0xf3, 0xe, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xb, 0xfd,
    0x0, 0x8f, 0xf2, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x1, 0xff, 0x70, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x5f, 0xf8, 0xff, 0x60, 0x0,
    0x0, 0x7f, 0xf2, 0x0, 0xc, 0xfe, 0x0, 0x0,
    0xb, 0xfd, 0xd, 0xfc, 0x0, 0x0, 0xd, 0xfb,
    0x0, 0x0, 0x6f, 0xf4, 0x0, 0x2, 0xff, 0x70,
    0x8f, 0xf2, 0x0, 0x3, 0xff, 0x60, 0x0, 0x1,
    0xff, 0xa0, 0x0, 0x8f, 0xf1, 0x2, 0xff, 0x80,
    0x0, 0x9f, 0xf0, 0x0, 0x0, 0xa, 0xff, 0x0,
    0xd, 0xfb, 0x0, 0xb, 0xfe, 0x0, 0xe, 0xfa,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x4, 0xff, 0x50,
    0x0, 0x5f, 0xf4, 0x5, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xef, 0xb0, 0xaf, 0xe0, 0x0, 0x0, 0xff,
    0x90, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x2f, 0xf9, 0x0, 0x0, 0x9, 0xff, 0x1f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc, 0xff, 0x20,
    0x0, 0x0, 0x3f, 0xfc, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0x0,

    /* U+0078 "x" */
    0xd, 0xff, 0x50, 0x0, 0x0, 0x0, 0xbf, 0xf6,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x7f, 0xfa,
    0x0, 0x0, 0x5f, 0xfc, 0x0, 0x0, 0x3f, 0xfd,
    0x0, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x1e, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xdf, 0xf5, 0xb, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf9, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xfc, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfe, 0x10, 0x8f, 0xfa, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x40, 0x0, 0xbf, 0xf6, 0x0, 0x0, 0xa,
    0xff, 0x80, 0x0, 0x1, 0xef, 0xf3, 0x0, 0x6,
    0xff, 0xc0, 0x0, 0x0, 0x4, 0xff, 0xe1, 0x3,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x8, 0xff, 0xb0,

    /* U+0079 "y" */
    0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf9, 0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf2, 0x0, 0xef, 0xf1, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x8f, 0xf7, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x40, 0x0, 0x1f, 0xfd, 0x0,
    0x0, 0x0, 0xe, 0xfd, 0x0, 0x0, 0xa, 0xff,
    0x50, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0xcf, 0xf2, 0x0, 0x3, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x5f, 0xf9, 0x0, 0xa, 0xff, 0x10,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x10, 0x1f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x70, 0x8f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0,
    0xef, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfb, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x4, 0x0, 0x0, 0x6f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xe9, 0x7a, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbe, 0xfd,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x36, 0x66,
    0x66, 0x66, 0x6c, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x75, 0x55, 0x55,
    0x55, 0x52, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+007B "{" */
    0x0, 0x0, 0x19, 0xef, 0xf0, 0x0, 0x1e, 0xff,
    0xff, 0x0, 0x9, 0xff, 0xe7, 0x50, 0x0, 0xcf,
    0xf4, 0x0, 0x0, 0xe, 0xff, 0x0, 0x0, 0x0,
    0xef, 0xf0, 0x0, 0x0, 0xe, 0xff, 0x0, 0x0,
    0x0, 0xef, 0xf0, 0x0, 0x0, 0xe, 0xff, 0x0,
    0x0, 0x0, 0xef, 0xf0, 0x0, 0x0, 0xe, 0xff,
    0x0, 0x0, 0x0, 0xef, 0xf0, 0x0, 0x0, 0x4f,
    0xfd, 0x0, 0x4, 0xff, 0xff, 0x50, 0x0, 0x4f,
    0xff, 0xc2, 0x0, 0x1, 0x6a, 0xff, 0xc0, 0x0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xef, 0xf0,
    0x0, 0x0, 0xe, 0xff, 0x0, 0x0, 0x0, 0xef,
    0xf0, 0x0, 0x0, 0xe, 0xff, 0x0, 0x0, 0x0,
    0xef, 0xf0, 0x0, 0x0, 0xe, 0xff, 0x0, 0x0,
    0x0, 0xef, 0xf0, 0x0, 0x0, 0xd, 0xff, 0x30,
    0x0, 0x0, 0x9f, 0xfe, 0x75, 0x0, 0x2, 0xef,
    0xff, 0xf0, 0x0, 0x2, 0xae, 0xff,

    /* U+007C "|" */
    0xdf, 0xdd, 0xfd, 0xdf, 0xdd, 0xfd, 0xdf, 0xdd,
    0xfd, 0xdf, 0xdd, 0xfd, 0xdf, 0xdd, 0xfd, 0xdf,
    0xdd, 0xfd, 0xdf, 0xdd, 0xfd, 0xdf, 0xdd, 0xfd,
    0xdf, 0xdd, 0xfd, 0xdf, 0xdd, 0xfd, 0xdf, 0xdd,
    0xfd, 0xdf, 0xdd, 0xfd, 0xdf, 0xdd, 0xfd, 0xdf,
    0xdd, 0xfd,

    /* U+007D "}" */
    0x7f, 0xfc, 0x60, 0x0, 0x7, 0xff, 0xff, 0x90,
    0x0, 0x26, 0xaf, 0xff, 0x20, 0x0, 0x0, 0xbf,
    0xf5, 0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x7, 0xff, 0x70, 0x0,
    0x0, 0x7f, 0xf7, 0x0, 0x0, 0x7, 0xff, 0x70,
    0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x7, 0xff,
    0x70, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x4,
    0xff, 0xc0, 0x0, 0x0, 0xc, 0xff, 0xfc, 0x0,
    0x0, 0x7f, 0xff, 0xc0, 0x0, 0x3f, 0xfe, 0x74,
    0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xf7,
    0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0, 0x7f,
    0xf7, 0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x7, 0xff, 0x70, 0x0,
    0x0, 0x7f, 0xf7, 0x0, 0x0, 0xb, 0xff, 0x60,
    0x2, 0x6a, 0xff, 0xf2, 0x0, 0x7f, 0xff, 0xf9,
    0x0, 0x7, 0xff, 0xc6, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x4c, 0xfe, 0x80, 0x0, 0x0, 0x3f, 0xa0,
    0x4f, 0xff, 0xff, 0xd2, 0x0, 0x7, 0xf8, 0xd,
    0xfc, 0x46, 0xef, 0xf6, 0x4, 0xef, 0x41, 0xff,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xb0, 0x3f, 0xb0,
    0x0, 0x0, 0x5c, 0xfe, 0x90, 0x0,

    /* U+00B0 "°" */
    0x0, 0x3, 0x78, 0x61, 0x0, 0x0, 0x1b, 0xff,
    0xff, 0xf6, 0x0, 0xb, 0xf8, 0x20, 0x4d, 0xf5,
    0x4, 0xf8, 0x0, 0x0, 0x1e, 0xd0, 0x9f, 0x20,
    0x0, 0x0, 0x9f, 0x29, 0xf1, 0x0, 0x0, 0x8,
    0xf2, 0x7f, 0x50, 0x0, 0x0, 0xcf, 0x1, 0xfe,
    0x20, 0x0, 0x8f, 0x90, 0x4, 0xff, 0xca, 0xef,
    0xc0, 0x0, 0x2, 0xae, 0xfd, 0x70, 0x0,

    /* U+2022 "•" */
    0x0, 0x26, 0x40, 0x0, 0x5f, 0xff, 0xb0, 0xd,
    0xff, 0xff, 0x40, 0xff, 0xff, 0xf5, 0xa, 0xff,
    0xff, 0x10, 0x1a, 0xfd, 0x40,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x6b, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0xae, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x72, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x51, 0x0,
    0xe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xc8, 0x30, 0x0, 0x0, 0x0,
    0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xa6, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x35, 0x53, 0xef, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x7, 0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x16, 0x99,
    0x8c, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x3, 0xae, 0xff,
    0xfc, 0x60, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xac, 0xdc, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F008 "" */
    0x26, 0x0, 0x1, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x40, 0x0, 0x62, 0xee,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xee, 0xff, 0xba,
    0xad, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xea, 0xaa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x54, 0x48, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xb4, 0x44, 0xff, 0xfe, 0x0, 0x3, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80,
    0x0, 0xef, 0xfe, 0x0, 0x3, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80, 0x0,
    0xef, 0xff, 0x10, 0x5, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xee, 0xef, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xee, 0xff, 0xfe, 0x0,
    0x4, 0xff, 0xfa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xad, 0xff, 0x90, 0x0, 0xef, 0xfe, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0xef, 0xfe, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xef, 0xff, 0x76, 0x6a, 0xff, 0xc2,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x28, 0xff, 0xc6,
    0x66, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x98, 0x8b, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xd8, 0x88, 0xff,
    0xfe, 0x0, 0x3, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x80, 0x0, 0xef, 0xfe,
    0x0, 0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x80, 0x0, 0xef, 0xfe, 0x0,
    0x4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x80, 0x0, 0xef, 0xff, 0xcc, 0xce,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xfc, 0xcc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x32, 0x27, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x22, 0xff, 0x9d, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0xd9,

    /* U+F00B "" */
    0x26, 0x77, 0x77, 0x76, 0x0, 0x4, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x62, 0xef,
    0xff, 0xff, 0xff, 0x90, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0x50, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0x50, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0x90, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x27,
    0x88, 0x88, 0x87, 0x10, 0x5, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x88, 0x88,
    0x87, 0x10, 0x6, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x83, 0xef, 0xff, 0xff, 0xff,
    0xa0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xfe, 0x40, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x6e, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x6,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xa3,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xad,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F00D "" */
    0x0, 0x67, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x30, 0x0, 0xbf, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x50, 0x9f, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x3f,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xf9, 0xbf, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0x51, 0xcf, 0xff, 0xff,
    0xfd, 0x10, 0x5f, 0xff, 0xff, 0xff, 0x70, 0x1,
    0xcf, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0x71, 0xcf, 0xff, 0xff, 0xfd, 0x10,
    0x5f, 0xff, 0xff, 0xff, 0x70, 0x1, 0xcf, 0xff,
    0xff, 0xfd, 0x1e, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xf8, 0xdf, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0x73,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xff, 0xc0, 0x4, 0xef, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x10, 0x0, 0x2f, 0xff, 0xf9, 0x0, 0x0,
    0x45, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xc0, 0x0, 0x2f, 0xff, 0xf9, 0x0, 0x5, 0xff,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x0, 0x2f, 0xff, 0xf9, 0x0, 0xd, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xfa, 0x0,
    0x2f, 0xff, 0xf9, 0x0, 0x3f, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xf4, 0x0, 0x2f,
    0xff, 0xf9, 0x0, 0xd, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0x40, 0x0, 0x2f, 0xff,
    0xf9, 0x0, 0x1, 0xcf, 0xff, 0xff, 0x10, 0x2,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x2f, 0xff, 0xf9,
    0x0, 0x0, 0xd, 0xff, 0xff, 0x90, 0x8, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x2f, 0xff, 0xf9, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf1, 0xe, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x2f, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf5, 0x2f, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf9, 0x5f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xfc, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xfe, 0x8f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfe, 0x6f,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x6, 0xbb, 0xa1,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xfd, 0x3f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xfb, 0xf, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf7, 0xa, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xf2, 0x4, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xb0, 0x0, 0xcf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0x30, 0x0, 0x2f, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0,
    0x0, 0x17, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xeb, 0x99, 0xad,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xef, 0xff, 0xff, 0xc8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xcc, 0xb8,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0, 0x1,
    0x8f, 0xff, 0xff, 0xff, 0xf8, 0x10, 0x0, 0x12,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x70, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x7, 0xff, 0x30,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc5, 0x11, 0x5c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x3, 0xdf, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xfd, 0x30, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x5e,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xe5, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x99, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x8, 0xff, 0xe7, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x7e, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x99, 0x10, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x44, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0x34, 0x44, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0xff, 0xb1, 0x0, 0x1, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xfd, 0x20, 0x1, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xf4, 0x1, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x71,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xdf, 0xff, 0xfc, 0x24, 0xef, 0xff,
    0xfb, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xa0, 0x0, 0x2d,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf7, 0x0, 0x75,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x50, 0x1b,
    0xff, 0x80, 0x8, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xe3, 0x2,
    0xdf, 0xff, 0xfb, 0x0, 0x5f, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xfc, 0x10,
    0x4f, 0xff, 0xff, 0xff, 0xd2, 0x3, 0xef, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xa0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x1c,
    0xff, 0xff, 0xd2, 0x0, 0x8, 0xff, 0xff, 0xf7,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0xaf, 0xff, 0xff, 0x40, 0xaf, 0xff, 0xff,
    0x40, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x7, 0xff, 0xff, 0xf6, 0xcf, 0xff,
    0xd2, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x10, 0x5f, 0xff, 0xf8, 0x2e,
    0xfb, 0x10, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd2, 0x3, 0xef, 0xc0,
    0x3, 0x70, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x18,
    0x10, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xa7, 0x77, 0x7d, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xcd,
    0xdd, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0xdd, 0xdc,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x77, 0x77, 0x77, 0x77,
    0x10, 0x4f, 0xff, 0xf5, 0x1, 0x77, 0x77, 0x77,
    0x77, 0x62, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x4, 0xff, 0x40, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x22, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x8f, 0x60, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x8f,
    0x60, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xa5,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x2, 0x9b, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb8, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xe1, 0x0,
    0x0, 0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfa,
    0x0, 0x9, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0x50, 0x3f, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xe1, 0xcf, 0xff, 0xd4, 0x44, 0x44,
    0x43, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x44,
    0x44, 0x4f, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x88, 0x88,
    0x88, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x70,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x26, 0xab, 0xdd, 0xb8,
    0x40, 0x0, 0x0, 0x1, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x92, 0x0, 0x1, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0xff, 0xff, 0x0, 0x0, 0x4, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0xff, 0xff, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xb8, 0x66, 0x8c, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0x0, 0x3, 0xff, 0xff, 0xff, 0x81,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xd, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x9f, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x1,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x22, 0x10, 0xd, 0xff, 0xff, 0xff, 0x8, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2e, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x22, 0x10, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xf3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xb0, 0xff, 0xff, 0xff, 0xec, 0xcd,
    0xef, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0x50, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfd,
    0x0, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf5, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0x90, 0x0, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xfa, 0x51, 0x0, 0x14,
    0x9f, 0xff, 0xff, 0xfc, 0x0, 0x0, 0xff, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0xff, 0xff, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xff, 0xff, 0x10, 0x1,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x30,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x10, 0x0, 0x1,
    0x6b, 0xff, 0xff, 0xff, 0xd9, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x2, 0x43, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x44, 0x44, 0x44, 0x7f, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x9, 0xc4, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x2, 0xff, 0xf4, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xb, 0xff, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xa, 0xff, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x4f, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x5, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x2, 0xef,
    0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1, 0xef, 0xfb, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x1f, 0xfd, 0x10,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x36, 0x0, 0x0, 0x44, 0x44, 0x44, 0x7f,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf0, 0x0, 0x0, 0x4,
    0x92, 0x0, 0xb, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xf0, 0x0, 0x0,
    0xf, 0xff, 0x50, 0x0, 0xdf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xc, 0xff, 0xf5, 0x0, 0x3f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x20, 0x9, 0xff,
    0x70, 0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xc0, 0x2,
    0xff, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xad, 0x40, 0x0, 0xef, 0xf4,
    0x0, 0xcf, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x2, 0xff, 0xf5, 0x0, 0x6f,
    0xf9, 0x0, 0x7f, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xaf, 0xff, 0x0,
    0xf, 0xfe, 0x0, 0x4f, 0xf9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xa, 0xff,
    0x60, 0xc, 0xff, 0x0, 0x2f, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x4,
    0xff, 0x80, 0xb, 0xff, 0x10, 0x1f, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x6, 0xff, 0x70, 0xc, 0xff, 0x10, 0x2f, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x3e, 0xff, 0x30, 0xe, 0xff, 0x0, 0x3f,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1, 0xff, 0xfb, 0x0, 0x3f, 0xfc, 0x0,
    0x6f, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x1, 0xff, 0xc1, 0x0, 0xaf, 0xf7,
    0x0, 0xaf, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x35, 0x0, 0x4, 0xff,
    0xf1, 0x0, 0xef, 0xf0, 0x4, 0x44, 0x44, 0x47,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x70, 0x6, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x6,
    0xff, 0xfc, 0x0, 0xd, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0xf, 0xff, 0xc1, 0x0, 0x8f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf0, 0x0,
    0x0, 0xc, 0xf9, 0x0, 0x4, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x3f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x60, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x45, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x54, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x2,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xbd, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x1c, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xcf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xc,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8e,
    0xff, 0xf9, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbe, 0xff, 0xd0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xcf,
    0xff, 0x10, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x87, 0xff, 0xf8, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x2f, 0xff, 0xf4, 0x2,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x9f,
    0xff, 0xf4, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xff,
    0x50, 0x1, 0xef, 0xff, 0xfb, 0x41, 0xc, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x9a,
    0xba, 0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x4, 0x44, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x10, 0x3f, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xf2, 0x4f, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xf7, 0x4f, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf8,
    0x4f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff,
    0xf0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xf0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xf0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff,
    0xf0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xf0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xf8, 0x4f, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf8, 0x4f, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf6, 0x2e, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfa,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xba, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x34, 0x44, 0x44, 0x41, 0x0, 0x0, 0x0,
    0x3, 0x44, 0x44, 0x44, 0x10, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0xff, 0x50, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x29, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x9,
    0xef, 0xff, 0xff, 0xeb, 0x20, 0x0, 0x0, 0x9e,
    0xff, 0xff, 0xfe, 0xb2, 0x0,

    /* U+F04D "" */
    0x0, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x10, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x29, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x9,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0,

    /* U+F051 "" */
    0x1, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x44, 0x40, 0x2e, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xf4, 0x6f, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x7f, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf4,
    0x7f, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0xe, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xe, 0xff, 0xf4,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xe,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0xe, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xe, 0xff, 0xf4, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf4,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7e,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xe, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0xe, 0xff, 0xf4, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0xe, 0xff, 0xf4,
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0, 0xe,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0xe, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x7f, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf4,
    0x7f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf4, 0x6f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xf4, 0xa, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x6a,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x20,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x4, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x70,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x27, 0x10, 0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x56, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x11, 0x11, 0xbf, 0xff, 0xff, 0x11,
    0x11, 0x11, 0x11, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1,
    0x17, 0x99, 0x99, 0x99, 0x99, 0xdf, 0xff, 0xff,
    0x99, 0x99, 0x99, 0x99, 0x82, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xde, 0xc3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x3c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x17, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x82, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x89,
    0xaa, 0x87, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xfd, 0x84, 0x23, 0x49, 0xef, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x5, 0xdd, 0x93,
    0x0, 0x6, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2, 0xff,
    0xff, 0x80, 0x0, 0xcf, 0xff, 0xff, 0xf6, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xf6, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0x30, 0x1f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0x10, 0xf, 0xff, 0xff,
    0xff, 0xc0, 0xaf, 0xff, 0xff, 0xff, 0x80, 0x5,
    0x44, 0x9f, 0xff, 0xff, 0xff, 0x50, 0xc, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0x70,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xb,
    0xff, 0xff, 0xff, 0xfb, 0xcf, 0xff, 0xff, 0xff,
    0x80, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xc, 0xff, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff,
    0xff, 0xa0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0xe, 0xff, 0xff, 0xff, 0xe1, 0x9, 0xff,
    0xff, 0xff, 0xe0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x2f, 0xff, 0xff, 0xff, 0x60, 0x0,
    0xdf, 0xff, 0xff, 0xf5, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x9f, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xfd, 0x0, 0x3, 0xdf,
    0xff, 0xfb, 0x10, 0x3, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x2, 0x55, 0x10, 0x0, 0x1e, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf8, 0x20, 0x0, 0x3, 0xaf, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xdf, 0xff, 0xff, 0xff, 0xdd, 0xff, 0xff, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x7a, 0xde, 0xff, 0xec,
    0x96, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x3, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x4, 0x79, 0xa9, 0x87, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xb1, 0x2, 0x8c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xfd, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x94, 0x23, 0x59,
    0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xe3, 0x6, 0xdc,
    0x92, 0x0, 0x9, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x54, 0xff, 0xff, 0x60, 0x0, 0xef, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xf5, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xf5,
    0x0, 0x6f, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x2f, 0xff, 0x90, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0xbf, 0xff, 0xfc, 0x10, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0xe,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x50, 0xd, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x40, 0xe, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xf6, 0x5f, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0xff, 0xff, 0xe7, 0x10, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff, 0xff,
    0xfe, 0xde, 0xc1, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xdf, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x7b, 0xde, 0xff,
    0xec, 0x80, 0x0, 0x0, 0x6, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4a, 0x20,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xdf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xf7, 0x11, 0x11,
    0xbf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdc, 0xcd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb5, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x81, 0x2a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x7c, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc5, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf6, 0x0, 0x8a, 0xaa, 0xaa, 0xa7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xaa, 0xdf,
    0xff, 0xff, 0x60, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xf5, 0x2, 0xef, 0xff,
    0xff, 0x40, 0x8f, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0x70, 0x1e, 0xff, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xe8, 0x1, 0xdf, 0xff, 0xff, 0x50, 0x0,
    0x7f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x1d, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x1a,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x3, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0x90, 0x1a, 0x10, 0x0, 0x5f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfa, 0x0,
    0xcf, 0xd1, 0x0, 0x7f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xb0, 0xb, 0xff,
    0xfc, 0x0, 0x8f, 0xff, 0xf6, 0x0, 0x8a, 0xaa,
    0xab, 0xff, 0xff, 0xfb, 0x0, 0x5f, 0xff, 0xff,
    0xca, 0xdf, 0xff, 0xff, 0x60, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xef, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1a, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xde, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xa6, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0x0, 0x6f,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xa0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf3, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0x30, 0x1d, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xf3, 0x9f, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfd, 0x6f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xfa, 0x8, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x46, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xde, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xce, 0x40, 0x1e, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xf4, 0x9f,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xfd, 0x5f, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xf9, 0x7, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xa0, 0x0, 0x7f,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xf3, 0x0, 0x1d,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0x31, 0xdf, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x56, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x2a, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0x90, 0x0, 0x5, 0xab, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x3f, 0xff, 0xfe, 0xff, 0xfe, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x10, 0x0, 0x0, 0x8f, 0xff, 0xe4,
    0xff, 0xf9, 0x9f, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0,
    0x3f, 0xfe, 0x22, 0xff, 0xf9, 0xa, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x10, 0x0, 0x0, 0x3, 0x82, 0x2, 0xff, 0xf9,
    0x0, 0x76, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x0, 0xaf, 0xff, 0x10,
    0x2, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x40,
    0xaf, 0xff, 0x10, 0xcf, 0xb0, 0x0, 0x0, 0x2,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xf3, 0xaf, 0xff, 0x1c, 0xff, 0xf7,
    0x0, 0x0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xdf, 0xff,
    0xcf, 0xff, 0xf4, 0x0, 0x0, 0x2, 0xff, 0xfe,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb5, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x2e, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x2, 0x68, 0x88, 0x88, 0x88, 0x88, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xca, 0x50, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xec, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33,
    0x33, 0x3e, 0xff, 0xff, 0xff, 0xa3, 0x33, 0x32,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x77, 0x77, 0x77, 0x72,
    0xe, 0xff, 0xff, 0xff, 0x90, 0x27, 0x77, 0x77,
    0x77, 0x62, 0xdf, 0xff, 0xff, 0xff, 0xf7, 0xb,
    0xff, 0xff, 0xff, 0x60, 0x7f, 0xff, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x45,
    0x55, 0x53, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x8f, 0x60, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x8f,
    0x60, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xa5,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xc8, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xfd, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x7d, 0xd1, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x29, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x5e, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x73, 0xbf, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xfe, 0xb6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xba, 0x98, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x0, 0x2, 0x79, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x8a, 0x83, 0x0, 0xb, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf9,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xff,
    0xa3, 0x7f, 0xff, 0xf0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xf5, 0xf, 0xff, 0xe0, 0x0, 0xaf,
    0xff, 0x20, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0xff, 0xfd, 0x0, 0x9, 0xff, 0xf3, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xd, 0xff,
    0xf5, 0x2, 0xff, 0xff, 0x10, 0x7, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0xff,
    0xff, 0xf2, 0x7, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xe8,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xde,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0x9b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xb, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0xc, 0xff, 0xfa, 0x37, 0xff, 0xff, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0xff, 0xfe,
    0x0, 0xa, 0xff, 0xf2, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0xf, 0xff, 0xd0, 0x0, 0x9f,
    0xff, 0x30, 0x0, 0xb, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0xdf, 0xff, 0x50, 0x2f, 0xff, 0xf1, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xe2, 0x7, 0xff,
    0xff, 0xef, 0xff, 0xfb, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xe1, 0xd, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xfd, 0x10, 0x2d, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xce, 0xd8, 0x0, 0x0,
    0x7, 0xbd, 0xc8, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x8,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x8f, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x8, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x8f, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x8, 0xff, 0xff,
    0xd0, 0x16, 0x66, 0x64, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x59, 0x99, 0x99, 0xd, 0xff,
    0xff, 0xa0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0xdd,
    0xdd, 0x1f, 0xff, 0xff, 0xa0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xfa, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xa0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xff, 0xa0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xff, 0xff, 0xa0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xfa, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff,
    0xa0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xfa, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xa0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xfa, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xa0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff,
    0xff, 0xa0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfc, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xf, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xe6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x40, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x3, 0x89, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x96, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0xff, 0xfe,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x7f,
    0xff, 0xfd, 0x10, 0xf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xfd,
    0x10, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfc, 0xf, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0x6f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x59, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x9,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc3, 0x0,

    /* U+F0C9 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x24, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x21, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x20,

    /* U+F0E0 "" */
    0x0, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x0, 0x2d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0xc3, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x5f, 0xff,
    0x70, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x9, 0xff, 0xff, 0xfb,
    0x10, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe3, 0x2, 0xdf, 0xff, 0xff, 0xff, 0xe4,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x10, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x1a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x2d, 0xff, 0xff, 0xff, 0xd2, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x9f, 0xff, 0xf9, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x3,
    0x99, 0x30, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xbb, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90,

    /* U+F0E7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xaa, 0xaa, 0xaa, 0x92, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x6, 0xcd, 0xdd, 0xdd, 0xdf, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x13, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0x66, 0x66, 0xcf, 0xff,
    0xff, 0xb6, 0x66, 0x65, 0x10, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xf8, 0x29, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xf8,
    0x29, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x54, 0x44, 0x44, 0x44, 0x30, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfb, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xe0, 0x8b, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x80, 0x6f, 0xff, 0xff, 0xff, 0xfe, 0x8, 0xfc,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x8f, 0xfc, 0x0, 0xf,
    0xff, 0xff, 0xff, 0x80, 0x7f, 0xff, 0xff, 0xff,
    0xfe, 0x8, 0xff, 0xfc, 0x0, 0xff, 0xff, 0xff,
    0xf8, 0x7, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x8f,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0x80, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0x5, 0xaa, 0xaa, 0xa0,
    0xff, 0xff, 0xff, 0xf8, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x80, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x82,
    0x22, 0x22, 0x20, 0xff, 0xff, 0xff, 0xf8, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0x80, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xf8, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0x80,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff,
    0xff, 0xff, 0x80, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xdf, 0xff, 0xff, 0xf8,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x11, 0x55, 0x55, 0x55, 0x20, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x29, 0xef, 0xff, 0xff, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xab, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x5, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb9,
    0x30, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xfd, 0x10, 0x1d,
    0xf5, 0x0, 0x1f, 0xf2, 0x0, 0x4f, 0xe1, 0x1,
    0xef, 0x40, 0x2, 0xff, 0xfc, 0xff, 0xfc, 0x0,
    0xb, 0xf2, 0x0, 0xe, 0xf0, 0x0, 0x1f, 0xc0,
    0x0, 0xcf, 0x10, 0x0, 0xff, 0xfc, 0xff, 0xfc,
    0x0, 0xb, 0xf2, 0x0, 0xe, 0xf0, 0x0, 0x1f,
    0xc0, 0x0, 0xcf, 0x10, 0x0, 0xff, 0xfc, 0xff,
    0xfd, 0x0, 0xd, 0xf4, 0x0, 0x1f, 0xf2, 0x0,
    0x3f, 0xe0, 0x0, 0xef, 0x30, 0x2, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xfe, 0xee, 0xff, 0xfe,
    0xef, 0xff, 0xee, 0xef, 0xff, 0xee, 0xef, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xdf,
    0x20, 0x1, 0xff, 0x0, 0x6, 0xf6, 0x0, 0xd,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x90, 0x0,
    0xcf, 0x10, 0x0, 0xff, 0x0, 0x5, 0xf5, 0x0,
    0xd, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x90,
    0x0, 0xcf, 0x10, 0x0, 0xff, 0x0, 0x5, 0xf5,
    0x0, 0xd, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xb2, 0x23, 0xef, 0x52, 0x24, 0xff, 0x32, 0x29,
    0xf9, 0x22, 0x3e, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xbb, 0xbf, 0xfd, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbc, 0xff, 0xdb, 0xbd, 0xff, 0xfc,
    0xff, 0xfc, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0xff,
    0xfc, 0xff, 0xfc, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0,
    0xff, 0xfc, 0xff, 0xfc, 0x0, 0xb, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0xff, 0xfc, 0xff, 0xff, 0xcc, 0xcf, 0xfd,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xff,
    0xdc, 0xcd, 0xff, 0xfc, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x70,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xdf, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x29, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x59, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xaa, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x26, 0x77, 0x77, 0x77, 0x77, 0x77, 0x70, 0x6,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0xfd, 0x10, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf, 0xfd,
    0x10, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0xff, 0xfd, 0x10, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xf, 0xff, 0xfd,
    0x10, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0xff, 0xff, 0xfd, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xf, 0xff, 0xff, 0xfd,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x4a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xa9, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x45, 0x65, 0x43, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xad, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xc8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xb8, 0x65, 0x55, 0x67, 0x9c, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xd7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7f, 0xff,
    0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x9f, 0xff, 0xff, 0xf7, 0x6f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff,
    0xc1, 0x6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x3,
    0x8b, 0xdf, 0xff, 0xec, 0x96, 0x10, 0x0, 0x0,
    0x0, 0x1c, 0xfc, 0x10, 0x0, 0x43, 0x0, 0x0,
    0x0, 0x18, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x40, 0x0, 0x0, 0x0, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xec, 0xbc, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfc, 0x61,
    0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x98, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0xdc, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x5, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb9, 0x30, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe4, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xcd, 0xff, 0xfa,
    0xff, 0xfc, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1, 0xff, 0xfa, 0xff, 0xfc, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x1, 0xff, 0xfa, 0xff, 0xfc,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x1, 0xff,
    0xfa, 0xff, 0xfc, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x67, 0xff, 0xfa, 0xff, 0xfc, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0x3, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf7, 0xff, 0xfe, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xfd, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x5, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb9, 0x30, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe4, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xcd, 0xff, 0xfa,
    0xff, 0xfc, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfa, 0xff, 0xfc, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0xff, 0xfc,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xfa, 0xff, 0xfc, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x67, 0xff, 0xfa, 0xff, 0xfc, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0x1, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x40, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf7, 0xff, 0xfe, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xfd, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x5, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb9, 0x30, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe4, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0xff, 0xfa,
    0xff, 0xfc, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfa, 0xff, 0xfc, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0xff, 0xfc,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xfa, 0xff, 0xfc, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x67, 0xff, 0xfa, 0xff, 0xfc, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0x1, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf7, 0xff, 0xfe, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xfd, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x5, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb9, 0x30, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe4, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x6, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0x6,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0xff, 0xfa,
    0xff, 0xfc, 0x6, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfa, 0xff, 0xfc, 0x6, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0xff, 0xfc,
    0x6, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xfa, 0xff, 0xfc, 0x6, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x67, 0xff, 0xfa, 0xff, 0xfc, 0x6, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0x3, 0x88, 0x88, 0x88, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf7, 0xff, 0xfe, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xfd, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x5, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb9, 0x30, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe4, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0xff, 0xfa,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x67, 0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf7, 0xff, 0xfe, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xfd, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xac, 0xcf,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xa5, 0x5e, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x3, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0x0, 0x0, 0x15, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x30,
    0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xdf, 0xff, 0x70, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x60, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf8, 0x0,
    0x7, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfc, 0x30, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0x32, 0x5f, 0xf7, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x2b, 0xff, 0xf9,
    0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0xaf,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x1c, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf7, 0x0, 0x2e, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfa, 0x10, 0x0, 0x2, 0xbf, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfa, 0x0, 0xf, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x60, 0xf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xef, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x2f, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x33, 0x33, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x9d, 0xff, 0xff, 0xfd, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xf9, 0xef, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xf7, 0x2e, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xf7, 0x2, 0xef, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0x20, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x3, 0xff, 0xff, 0xff, 0x80,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x1, 0x20,
    0x4f, 0xff, 0xff, 0xd0, 0x6, 0xff, 0xff, 0xa7,
    0xff, 0xf7, 0x2, 0xe2, 0x5, 0xff, 0xff, 0xf0,
    0xa, 0xff, 0xfc, 0x0, 0x6f, 0xf7, 0x1, 0xfe,
    0x20, 0x5f, 0xff, 0xf3, 0xc, 0xff, 0xff, 0x70,
    0x6, 0xf7, 0x1, 0xfc, 0x0, 0x8f, 0xff, 0xf6,
    0xe, 0xff, 0xff, 0xf7, 0x0, 0x67, 0x1, 0xc0,
    0x7, 0xff, 0xff, 0xf7, 0xf, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xf9, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xfa,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xfa, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xf9,
    0xf, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xf9, 0xe, 0xff, 0xff, 0xf8,
    0x0, 0x47, 0x1, 0xa0, 0x6, 0xff, 0xff, 0xf7,
    0xc, 0xff, 0xff, 0x80, 0x4, 0xf7, 0x1, 0xfa,
    0x0, 0x7f, 0xff, 0xf6, 0xa, 0xff, 0xfc, 0x0,
    0x4f, 0xf8, 0x1, 0xfe, 0x20, 0x2e, 0xff, 0xf4,
    0x7, 0xff, 0xff, 0x95, 0xff, 0xf8, 0x2, 0xe3,
    0x2, 0xef, 0xff, 0xf1, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x1, 0x30, 0x2e, 0xff, 0xff, 0xd0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x2,
    0xef, 0xff, 0xff, 0x80, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x2e, 0xff, 0xff, 0xff, 0x20,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xf9, 0x3, 0xef,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xf9, 0x3e, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xfc, 0xef, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x58, 0xbc, 0xdd, 0xca,
    0x61, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x77, 0x77, 0x77,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x69, 0x99, 0x99, 0x99, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa9, 0x99, 0x99, 0x98,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x40, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x2, 0xff, 0xff, 0xb0, 0xbf, 0xff,
    0x62, 0xff, 0xfe, 0x18, 0xff, 0xff, 0x60, 0x0,
    0x2f, 0xff, 0xf9, 0x9, 0xff, 0xf3, 0xf, 0xff,
    0xd0, 0x5f, 0xff, 0xf6, 0x0, 0x2, 0xff, 0xff,
    0x90, 0x9f, 0xff, 0x30, 0xff, 0xfd, 0x5, 0xff,
    0xff, 0x60, 0x0, 0x2f, 0xff, 0xf9, 0x9, 0xff,
    0xf3, 0xf, 0xff, 0xd0, 0x5f, 0xff, 0xf6, 0x0,
    0x2, 0xff, 0xff, 0x90, 0x9f, 0xff, 0x30, 0xff,
    0xfd, 0x5, 0xff, 0xff, 0x60, 0x0, 0x2f, 0xff,
    0xf9, 0x9, 0xff, 0xf3, 0xf, 0xff, 0xd0, 0x5f,
    0xff, 0xf6, 0x0, 0x2, 0xff, 0xff, 0x90, 0x9f,
    0xff, 0x30, 0xff, 0xfd, 0x5, 0xff, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0xf9, 0x9, 0xff, 0xf3, 0xf,
    0xff, 0xd0, 0x5f, 0xff, 0xf6, 0x0, 0x2, 0xff,
    0xff, 0x90, 0x9f, 0xff, 0x30, 0xff, 0xfd, 0x5,
    0xff, 0xff, 0x60, 0x0, 0x2f, 0xff, 0xf9, 0x9,
    0xff, 0xf3, 0xf, 0xff, 0xd0, 0x5f, 0xff, 0xf6,
    0x0, 0x2, 0xff, 0xff, 0x90, 0x9f, 0xff, 0x30,
    0xff, 0xfd, 0x5, 0xff, 0xff, 0x60, 0x0, 0x2f,
    0xff, 0xf9, 0x9, 0xff, 0xf3, 0xf, 0xff, 0xd0,
    0x5f, 0xff, 0xf6, 0x0, 0x2, 0xff, 0xff, 0x90,
    0x9f, 0xff, 0x30, 0xff, 0xfd, 0x5, 0xff, 0xff,
    0x60, 0x0, 0x2f, 0xff, 0xf9, 0x9, 0xff, 0xf3,
    0xf, 0xff, 0xd0, 0x5f, 0xff, 0xf6, 0x0, 0x2,
    0xff, 0xff, 0xb0, 0xbf, 0xff, 0x62, 0xff, 0xfe,
    0x18, 0xff, 0xff, 0x60, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x7, 0xbc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x81, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xef, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x50, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x2e,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x2e,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x50, 0x2e,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0x50, 0x2e,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x2e,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xba, 0x86, 0x43, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0x78, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4e, 0xff, 0xff, 0xfe, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x2, 0xef, 0xff, 0xe3, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x2e,
    0xfe, 0x30, 0x0, 0xf, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x2, 0xd3, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xf8, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x50, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x9f, 0xff, 0x90, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x9, 0xff, 0xff, 0xf9,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xcf,
    0xff, 0xff, 0xff, 0xce, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe9, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x3,
    0xff, 0xdb, 0xbf, 0xfb, 0xbb, 0xff, 0xbb, 0xbf,
    0xff, 0xa0, 0x3, 0xff, 0xf9, 0x0, 0xcf, 0x10,
    0xf, 0xf0, 0x1, 0xff, 0xfa, 0x4, 0xff, 0xff,
    0x90, 0xc, 0xf1, 0x0, 0xff, 0x0, 0x1f, 0xff,
    0xa4, 0xff, 0xff, 0xf9, 0x0, 0xcf, 0x10, 0xf,
    0xf0, 0x1, 0xff, 0xfa, 0xff, 0xff, 0xff, 0x90,
    0xc, 0xf1, 0x0, 0xff, 0x0, 0x1f, 0xff, 0xaf,
    0xff, 0xff, 0xf9, 0x0, 0xcf, 0x10, 0xf, 0xf0,
    0x1, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xae, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x41, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x6a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xa9, 0x40, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x1b, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x2d, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x2e,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0x10, 0x0, 0x3e, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0x0, 0x4f, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0x10, 0x5f, 0xff, 0xff, 0xff,
    0xdb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xef, 0xff, 0xf1, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 129, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 129, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 42, .adv_w = 188, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 83, .adv_w = 337, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 304, .adv_w = 298, .box_w = 17, .box_h = 29, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 551, .adv_w = 405, .box_w = 24, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 803, .adv_w = 329, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1023, .adv_w = 101, .box_w = 4, .box_h = 9, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 1041, .adv_w = 162, .box_w = 8, .box_h = 28, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 1153, .adv_w = 162, .box_w = 8, .box_h = 28, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 1265, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 1337, .adv_w = 279, .box_w = 14, .box_h = 14, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 1435, .adv_w = 109, .box_w = 5, .box_h = 9, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1458, .adv_w = 184, .box_w = 9, .box_h = 3, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 1472, .adv_w = 109, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1485, .adv_w = 169, .box_w = 13, .box_h = 28, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1667, .adv_w = 320, .box_w = 18, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1856, .adv_w = 178, .box_w = 8, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1940, .adv_w = 276, .box_w = 17, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2119, .adv_w = 275, .box_w = 16, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2287, .adv_w = 321, .box_w = 19, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2487, .adv_w = 276, .box_w = 17, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2666, .adv_w = 296, .box_w = 17, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2845, .adv_w = 287, .box_w = 17, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3024, .adv_w = 309, .box_w = 17, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3203, .adv_w = 296, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3392, .adv_w = 109, .box_w = 5, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3432, .adv_w = 109, .box_w = 5, .box_h = 21, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3485, .adv_w = 279, .box_w = 14, .box_h = 15, .ofs_x = 2, .ofs_y = 3},
    {.bitmap_index = 3590, .adv_w = 279, .box_w = 14, .box_h = 10, .ofs_x = 2, .ofs_y = 6},
    {.bitmap_index = 3660, .adv_w = 279, .box_w = 14, .box_h = 15, .ofs_x = 2, .ofs_y = 3},
    {.bitmap_index = 3765, .adv_w = 275, .box_w = 16, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3933, .adv_w = 496, .box_w = 29, .box_h = 27, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 4325, .adv_w = 351, .box_w = 23, .box_h = 21, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4567, .adv_w = 363, .box_w = 19, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 4767, .adv_w = 347, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4977, .adv_w = 396, .box_w = 21, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5198, .adv_w = 322, .box_w = 16, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5366, .adv_w = 305, .box_w = 15, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5524, .adv_w = 371, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5734, .adv_w = 390, .box_w = 19, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5934, .adv_w = 149, .box_w = 4, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 5976, .adv_w = 246, .box_w = 14, .box_h = 21, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6123, .adv_w = 345, .box_w = 19, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6323, .adv_w = 285, .box_w = 15, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6481, .adv_w = 458, .box_w = 23, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6723, .adv_w = 390, .box_w = 19, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6923, .adv_w = 403, .box_w = 23, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7165, .adv_w = 347, .box_w = 18, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7354, .adv_w = 403, .box_w = 24, .box_h = 26, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7666, .adv_w = 349, .box_w = 18, .box_h = 21, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7855, .adv_w = 298, .box_w = 17, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8034, .adv_w = 282, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8223, .adv_w = 380, .box_w = 19, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8423, .adv_w = 342, .box_w = 23, .box_h = 21, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8665, .adv_w = 540, .box_w = 33, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9012, .adv_w = 323, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9222, .adv_w = 311, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9443, .adv_w = 315, .box_w = 18, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9632, .adv_w = 160, .box_w = 7, .box_h = 28, .ofs_x = 3, .ofs_y = -6},
    {.bitmap_index = 9730, .adv_w = 169, .box_w = 14, .box_h = 28, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 9926, .adv_w = 160, .box_w = 7, .box_h = 28, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 10024, .adv_w = 280, .box_w = 14, .box_h = 13, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 10115, .adv_w = 240, .box_w = 15, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10130, .adv_w = 288, .box_w = 9, .box_h = 4, .ofs_x = 3, .ofs_y = 18},
    {.bitmap_index = 10148, .adv_w = 287, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10268, .adv_w = 327, .box_w = 18, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10466, .adv_w = 274, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10594, .adv_w = 327, .box_w = 17, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10781, .adv_w = 294, .box_w = 17, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10917, .adv_w = 169, .box_w = 12, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11049, .adv_w = 331, .box_w = 17, .box_h = 22, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 11236, .adv_w = 327, .box_w = 16, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11412, .adv_w = 134, .box_w = 5, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11467, .adv_w = 136, .box_w = 10, .box_h = 28, .ofs_x = -3, .ofs_y = -6},
    {.bitmap_index = 11607, .adv_w = 296, .box_w = 17, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11794, .adv_w = 134, .box_w = 4, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 11838, .adv_w = 507, .box_w = 28, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 12062, .adv_w = 327, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 12190, .adv_w = 305, .box_w = 17, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12326, .adv_w = 327, .box_w = 18, .box_h = 22, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 12524, .adv_w = 327, .box_w = 17, .box_h = 22, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 12711, .adv_w = 197, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 12791, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12911, .adv_w = 199, .box_w = 12, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13031, .adv_w = 325, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 13159, .adv_w = 268, .box_w = 18, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 13303, .adv_w = 432, .box_w = 27, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13519, .adv_w = 265, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13655, .adv_w = 268, .box_w = 18, .box_h = 22, .ofs_x = -1, .ofs_y = -6},
    {.bitmap_index = 13853, .adv_w = 250, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13965, .adv_w = 168, .box_w = 9, .box_h = 28, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 14091, .adv_w = 144, .box_w = 3, .box_h = 28, .ofs_x = 3, .ofs_y = -6},
    {.bitmap_index = 14133, .adv_w = 168, .box_w = 9, .box_h = 28, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 14259, .adv_w = 279, .box_w = 15, .box_h = 5, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 14297, .adv_w = 201, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 14352, .adv_w = 151, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 14373, .adv_w = 480, .box_w = 31, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 14854, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15199, .adv_w = 480, .box_w = 30, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15604, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15949, .adv_w = 330, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16180, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 16645, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 17110, .adv_w = 540, .box_w = 34, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17569, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 18034, .adv_w = 540, .box_w = 34, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18425, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 18890, .adv_w = 240, .box_w = 15, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 19070, .adv_w = 360, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 19346, .adv_w = 540, .box_w = 34, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 19839, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20184, .adv_w = 330, .box_w = 21, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 20510, .adv_w = 420, .box_w = 20, .box_h = 28, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 20790, .adv_w = 420, .box_w = 27, .box_h = 32, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 21222, .adv_w = 420, .box_w = 27, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21587, .adv_w = 420, .box_w = 27, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21952, .adv_w = 420, .box_w = 20, .box_h = 28, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 22232, .adv_w = 420, .box_w = 28, .box_h = 27, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 22610, .adv_w = 300, .box_w = 17, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 22840, .adv_w = 300, .box_w = 17, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 23070, .adv_w = 420, .box_w = 27, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 23435, .adv_w = 420, .box_w = 27, .box_h = 7, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 23530, .adv_w = 540, .box_w = 34, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23921, .adv_w = 600, .box_w = 38, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 24510, .adv_w = 540, .box_w = 36, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25068, .adv_w = 480, .box_w = 30, .box_h = 28, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 25488, .adv_w = 420, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 25709, .adv_w = 420, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 25930, .adv_w = 600, .box_w = 38, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 26386, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 26731, .adv_w = 480, .box_w = 30, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 27196, .adv_w = 480, .box_w = 31, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 27677, .adv_w = 420, .box_w = 27, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 28042, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 28461, .adv_w = 420, .box_w = 27, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 28826, .adv_w = 420, .box_w = 27, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 29150, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 29495, .adv_w = 300, .box_w = 20, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 29805, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 30224, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 30643, .adv_w = 540, .box_w = 34, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 31034, .adv_w = 480, .box_w = 32, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 31530, .adv_w = 360, .box_w = 23, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 31887, .adv_w = 600, .box_w = 38, .box_h = 28, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 32419, .adv_w = 600, .box_w = 38, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 32799, .adv_w = 600, .box_w = 38, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 33179, .adv_w = 600, .box_w = 38, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 33559, .adv_w = 600, .box_w = 38, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 33939, .adv_w = 600, .box_w = 38, .box_h = 20, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 34319, .adv_w = 600, .box_w = 38, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 34775, .adv_w = 420, .box_w = 24, .box_h = 31, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 35147, .adv_w = 420, .box_w = 27, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 35566, .adv_w = 480, .box_w = 31, .box_h = 31, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 36047, .adv_w = 600, .box_w = 38, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 36484, .adv_w = 360, .box_w = 23, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 36841, .adv_w = 483, .box_w = 31, .box_h = 20, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 5, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 22, 0, 13, -11, 0, 0,
    0, 0, -26, -29, 3, 23, 11, 8,
    -19, 3, 24, 1, 20, 5, 15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 29, 4, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 0, -14, 0, 0, 0, 0,
    0, -10, 8, 10, 0, 0, -5, 0,
    -3, 5, 0, -5, 0, -5, -2, -10,
    0, 0, 0, 0, -5, 0, 0, -6,
    -7, 0, 0, -5, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    -5, 0, -7, 0, -13, 0, -58, 0,
    0, -10, 0, 10, 14, 0, 0, -10,
    5, 5, 16, 10, -8, 10, 0, 0,
    -27, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -18, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -13, -6, -24, 0, -19,
    -3, 0, 0, 0, 0, 1, 19, 0,
    -14, -4, -1, 1, 0, -8, 0, 0,
    -3, -36, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -38, -4, 18,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -20, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 16,
    0, 5, 0, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 18, 4,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -18, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    10, 5, 14, -5, 0, 0, 10, -5,
    -16, -66, 3, 13, 10, 1, -6, 0,
    17, 0, 15, 0, 15, 0, -45, 0,
    -6, 14, 0, 16, -5, 10, 5, 0,
    0, 1, -5, 0, 0, -8, 38, 0,
    38, 0, 14, 0, 20, 6, 8, 14,
    0, 0, 0, -18, 0, 0, 0, 0,
    1, -3, 0, 3, -9, -6, -10, 3,
    0, -5, 0, 0, 0, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -31, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -26, 0, -30, 0, 0, 0,
    0, -3, 0, 48, -6, -6, 5, 5,
    -4, 0, -6, 5, 0, 0, -25, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -47, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -30, 0, 29, 0, 0, -18, 0,
    16, 0, -33, -47, -33, -10, 14, 0,
    0, -32, 0, 6, -11, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 14, -59, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 23, 0, 3, 0, 0, 0,
    0, 0, 3, 3, -6, -10, 0, -1,
    -1, -5, 0, 0, -3, 0, 0, 0,
    -10, 0, -4, 0, -11, -10, 0, -12,
    -16, -16, -9, 0, -10, 0, -10, 0,
    0, 0, 0, -4, 0, 0, 5, 0,
    3, -5, 0, 1, 0, 0, 0, 5,
    -3, 0, 0, 0, -3, 5, 5, -1,
    0, 0, 0, -9, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 6, -3, 0,
    -6, 0, -8, 0, 0, -3, 0, 14,
    0, 0, -5, 0, 0, 0, 0, 0,
    -1, 1, -3, -3, 0, 0, -5, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -5, -6, 0,
    0, 0, 0, 0, 1, 0, 0, -3,
    0, -5, -5, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -3, -6, 0, -7, 0, -14,
    -3, -14, 10, 0, 0, -10, 5, 10,
    13, 0, -12, -1, -6, 0, -1, -23,
    5, -3, 3, -25, 5, 0, 0, 1,
    -25, 0, -25, -4, -42, -3, 0, -24,
    0, 10, 13, 0, 6, 0, 0, 0,
    0, 1, 0, -9, -6, 0, -14, 0,
    0, 0, -5, 0, 0, 0, -5, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -6, 0, 0, 0, 0, 0, 0, 0,
    -5, -5, 0, -3, -6, -4, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -6,
    0, -3, 0, -10, 5, 0, 0, -6,
    2, 5, 5, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -5, 0, -5, -3, -6, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    -4, 0, 0, 0, 0, -5, -7, 0,
    -9, 0, 14, -3, 1, -15, 0, 0,
    13, -24, -25, -20, -10, 5, 0, -4,
    -31, -9, 0, -9, 0, -10, 7, -9,
    -31, 0, -13, 0, 0, 2, -1, 4,
    -3, 0, 5, 0, -14, -18, 0, -24,
    -12, -10, -12, -14, -6, -13, -1, -9,
    -13, 3, 0, 1, 0, -5, 0, 0,
    0, 3, 0, 5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, -2, 0, -1, -5, 0, -8, -11,
    -11, -1, 0, -14, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 2,
    -3, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 23, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -9, 0, 0, 0, 0, -24, -14, 0,
    0, 0, -7, -24, 0, 0, -5, 5,
    0, -13, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, -9, 0,
    0, 0, 0, 6, 0, 3, -10, -10,
    0, -5, -5, -6, 0, 0, 0, 0,
    0, 0, -14, 0, -5, 0, -7, -5,
    0, -11, -12, -14, -4, 0, -10, 0,
    -14, 0, 0, 0, 0, 38, 0, 0,
    2, 0, 0, -6, 0, 5, 0, -21,
    0, 0, 0, 0, 0, -45, -9, 16,
    14, -4, -20, 0, 5, -7, 0, -24,
    -2, -6, 5, -34, -5, 6, 0, 7,
    -17, -7, -18, -16, -20, 0, 0, -29,
    0, 27, 0, 0, -2, 0, 0, 0,
    -2, -2, -5, -13, -16, -1, -45, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -2, -5, -7, 0, 0,
    -10, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -10, 0, 0, 10,
    -1, 6, 0, -11, 5, -3, -1, -12,
    -5, 0, -6, -5, -3, 0, -7, -8,
    0, 0, -4, -1, -3, -8, -6, 0,
    0, -5, 0, 5, -3, 0, -11, 0,
    0, 0, -10, 0, -8, 0, -8, -8,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 5, 0, -7, 0, -3, -6,
    -15, -3, -3, -3, -1, -3, -6, -1,
    0, 0, 0, 0, 0, -5, -4, -4,
    0, 0, 0, 0, 6, -3, 0, -3,
    0, 0, 0, -3, -6, -3, -4, -6,
    -4, 0, 4, 19, -1, 0, -13, 0,
    -3, 10, 0, -5, -20, -6, 7, 0,
    0, -23, -8, 5, -8, 3, 0, -3,
    -4, -15, 0, -7, 2, 0, 0, -8,
    0, 0, 0, 5, 5, -10, -9, 0,
    -8, -5, -7, -5, -5, 0, -8, 2,
    -9, -8, 14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -6,
    0, 0, -5, -5, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -7, 0, -10, 0, 0, 0, -16, 0,
    3, -11, 10, 1, -3, -23, 0, 0,
    -11, -5, 0, -19, -12, -13, 0, 0,
    -21, -5, -19, -18, -23, 0, -12, 0,
    4, 32, -6, 0, -11, -5, -1, -5,
    -8, -13, -9, -18, -20, -11, -5, 0,
    0, -3, 0, 1, 0, 0, -34, -4,
    14, 11, -11, -18, 0, 1, -15, 0,
    -24, -3, -5, 10, -44, -6, 1, 0,
    0, -31, -6, -25, -5, -35, 0, 0,
    -34, 0, 28, 1, 0, -3, 0, 0,
    0, 0, -2, -3, -18, -3, 0, -31,
    0, 0, 0, 0, -15, 0, -4, 0,
    -1, -13, -23, 0, 0, -2, -7, -14,
    -5, 0, -3, 0, 0, 0, 0, -22,
    -5, -16, -15, -4, -8, -12, -5, -8,
    0, -10, -4, -16, -7, 0, -6, -9,
    -5, -9, 0, 2, 0, -3, -16, 0,
    10, 0, -9, 0, 0, 0, 0, 6,
    0, 3, -10, 20, 0, -5, -5, -6,
    0, 0, 0, 0, 0, 0, -14, 0,
    -5, 0, -7, -5, 0, -11, -12, -14,
    -4, 0, -10, 4, 19, 0, 0, 0,
    0, 38, 0, 0, 2, 0, 0, -6,
    0, 5, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -3, -10, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -5, -5, 0, 0, -10,
    -5, 0, 0, -10, 0, 8, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 7, 10, 4, -4, 0, -15,
    -8, 0, 14, -16, -15, -10, -10, 19,
    9, 5, -42, -3, 10, -5, 0, -5,
    5, -5, -17, 0, -5, 5, -6, -4,
    -14, -4, 0, 0, 14, 10, 0, -13,
    0, -26, -6, 14, -6, -18, 1, -6,
    -16, -16, -5, 19, 5, 0, -7, 0,
    -13, 0, 4, 16, -11, -18, -19, -12,
    14, 0, 1, -35, -4, 5, -8, -3,
    -11, 0, -11, -18, -7, -7, -4, 0,
    0, -11, -10, -5, 0, 14, 11, -5,
    -26, 0, -26, -7, 0, -17, -28, -1,
    -15, -8, -16, -13, 13, 0, 0, -6,
    0, -10, -4, 0, -5, -9, 0, 8,
    -16, 5, 0, 0, -25, 0, -5, -11,
    -8, -3, -14, -12, -16, -11, 0, -14,
    -5, -11, -9, -14, -5, 0, 0, 1,
    23, -8, 0, -14, -5, 0, -5, -10,
    -11, -13, -13, -18, -6, -10, 10, 0,
    -7, 0, -24, -6, 3, 10, -15, -18,
    -10, -16, 16, -5, 2, -45, -9, 10,
    -11, -8, -18, 0, -14, -20, -6, -5,
    -4, -5, -10, -14, -1, 0, 0, 14,
    13, -3, -31, 0, -29, -11, 12, -18,
    -33, -10, -17, -20, -24, -16, 10, 0,
    0, 0, 0, -6, 0, 0, 5, -6,
    10, 3, -9, 10, 0, 0, -15, -1,
    0, -1, 0, 1, 1, -4, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 4, 14, 1, 0, -6, 0, 0,
    0, 0, -3, -3, -6, 0, 0, 0,
    1, 4, 0, 0, 0, 0, 4, 0,
    -4, 0, 18, 0, 9, 1, 1, -6,
    0, 10, 0, 0, 0, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 14, 0, 13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -29, 0, -5, 8, 0, 14,
    0, 0, 48, 6, -10, -10, 5, 5,
    -3, 1, -24, 0, 0, 23, -29, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -33, 18, 67, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -29, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, -9,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, -13, 0,
    0, 1, 0, 0, 5, 62, -10, -4,
    15, 13, -13, 5, 0, 0, 5, 5,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -62, 13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    0, 0, 0, -13, 0, 0, 0, 0,
    -11, -2, 0, 0, 0, -11, 0, -6,
    0, -23, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -32, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -5, 0, 0, -9, 0, -7, 0,
    -13, 0, 0, 0, -8, 5, -6, 0,
    0, -13, -5, -11, 0, 0, -13, 0,
    -5, 0, -23, 0, -5, 0, 0, -39,
    -9, -19, -5, -17, 0, 0, -32, 0,
    -13, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -7, -9, -4, -8, 0, 0,
    0, 0, -11, 0, -11, 6, -5, 10,
    0, -3, -11, -3, -8, -9, 0, -6,
    -2, -3, 3, -13, -1, 0, 0, 0,
    -42, -4, -7, 0, -11, 0, -3, -23,
    -4, 0, 0, -3, -4, 0, 0, 0,
    0, 3, 0, -3, -8, -3, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 0, 0, 0, 0,
    0, -11, 0, -3, 0, 0, 0, -10,
    5, 0, 0, 0, -13, -5, -10, 0,
    0, -13, 0, -5, 0, -23, 0, 0,
    0, 0, -47, 0, -10, -18, -24, 0,
    0, -32, 0, -3, -7, 0, 0, 0,
    0, 0, 0, 0, 0, -5, -7, -2,
    -7, 1, 0, 0, 8, -6, 0, 15,
    24, -5, -5, -14, 6, 24, 8, 11,
    -13, 6, 20, 6, 14, 11, 13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 30, 23, -9, -5, 0, -4,
    38, 21, 38, 0, 0, 0, 5, 0,
    0, 18, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 7,
    0, 0, 0, 0, -40, -6, -4, -20,
    -24, 0, 0, -32, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    7, 0, 0, 0, 0, -40, -6, -4,
    -20, -24, 0, 0, -19, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, -11, 5, 0, -5,
    4, 9, 5, -14, 0, -1, -4, 5,
    0, 4, 0, 0, 0, 0, -12, 0,
    -4, -3, -10, 0, -4, -19, 0, 30,
    -5, 0, -11, -3, 0, -3, -8, 0,
    -5, -13, -10, -6, 0, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 7, 0, 0, 0, 0, -40,
    -6, -4, -20, -24, 0, 0, -32, 0,
    0, 0, 0, 0, 0, 24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 0, -15, -6, -4, 14, -4, -5,
    -19, 1, -3, 1, -3, -13, 1, 11,
    1, 4, 1, 4, -12, -19, -6, 0,
    -18, -9, -13, -20, -19, 0, -8, -10,
    -6, -6, -4, -3, -6, -3, 0, -3,
    -1, 7, 0, 7, -3, 0, 15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, -5, -5, 0, 0,
    -13, 0, -2, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -29, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -5, 0, -6,
    0, 0, 0, 0, -4, 0, 0, -8,
    -5, 5, 0, -8, -9, -3, 0, -14,
    -3, -11, -3, -6, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -32, 0, 15, 0, 0, -9, 0,
    0, 0, 0, -6, 0, -5, 0, 0,
    -2, 0, 0, -3, 0, -11, 0, 0,
    20, -6, -16, -15, 3, 5, 5, -1,
    -13, 3, 7, 3, 14, 3, 16, -3,
    -13, 0, 0, -19, 0, 0, -14, -13,
    0, 0, -10, 0, -6, -8, 0, -7,
    0, -7, 0, -3, 7, 0, -4, -14,
    -5, 18, 0, 0, -4, 0, -10, 0,
    0, 6, -11, 0, 5, -5, 4, 0,
    0, -16, 0, -3, -1, 0, -5, 5,
    -4, 0, 0, 0, -20, -6, -11, 0,
    -14, 0, 0, -23, 0, 18, -5, 0,
    -9, 0, 3, 0, -5, 0, -5, -14,
    0, -5, 5, 0, 0, 0, 0, -3,
    0, 0, 5, -6, 1, 0, 0, -6,
    -3, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -30, 0, 11, 0,
    0, -4, 0, 0, 0, 0, 1, 0,
    -5, -5, 0, 0, 0, 10, 0, 11,
    0, 0, 0, 0, 0, -30, -27, 1,
    21, 14, 8, -19, 3, 20, 0, 18,
    0, 10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 25, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_30 = {
#else
lv_font_t lv_font_montserrat_30 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 33,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRAT_30*/

