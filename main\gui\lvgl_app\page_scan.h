#ifndef __page_menu_scan_H
#define __page_menu_scan_H



#ifdef __cplusplus
extern "C" {
#endif

    /*********************
    *      INCLUDES
    *********************/
#include "../lvgl/lvgl.h"
    /*********************
    *      DEFINES
    *********************/

    /**********************
    *      TYPEDEFS
    **********************/
    typedef enum
    {
        scan_chart_trigger = 0,
        scan_table_trigger,
    }scan_trigger;
    /**********************
    * GLOBAL PROTOTYPES
    **********************/
    extern lv_indev_t* indev_keypad;
    void page_scan_create(void);
    void page_scan_exit();
    void key_delay_time_scan(uint32_t time_out);
    /**********************
    *      MACROS
    **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

 


#endif
