#include <stdio.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_spi_flash.h"
#include "system.h"
#include "lvgl_init.h"
#include "gui/lvgl_app/page_backpack_handler.h"
#include "Backpack.h"
void app_main(void)
{

    system_init();
    lvgl_init();
    backpack_handler_init();
    // backpack_init_task();
    while (1)
    {
        lv_task_handler();
        vTaskDelay(5 / portTICK_PERIOD_MS);  // Reduced from 10ms for better GUI responsiveness
    }
}
