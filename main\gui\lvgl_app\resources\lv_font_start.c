 /*******************************************************************************
 * Size: 28 px
 * Bpp: 4
 * Opts:
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif

#ifndef LV_FONT_START
#define LV_FONT_START 1
#endif

#if LV_FONT_START

 /*-----------------
  *    BITMAPS
  *----------------*/

  /*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    
   /* U+0020 " " */

    /* U+0048 "H" */
    0xbf, 0xf0, 0x0, 0x0, 0x5, 0xff, 0x5b, 0xff,
    0x0, 0x0, 0x0, 0x5f, 0xf5, 0xbf, 0xf0, 0x0,
    0x0, 0x5, 0xff, 0x5b, 0xff, 0x0, 0x0, 0x0,
    0x5f, 0xf5, 0xbf, 0xf0, 0x0, 0x0, 0x5, 0xff,
    0x5b, 0xff, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0xbf,
    0xf0, 0x0, 0x0, 0x5, 0xff, 0x5b, 0xff, 0x0,
    0x0, 0x0, 0x5f, 0xf5, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xbf, 0xf3, 0x33, 0x33, 0x37, 0xff, 0x5b,
    0xff, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0xbf, 0xf0,
    0x0, 0x0, 0x5, 0xff, 0x5b, 0xff, 0x0, 0x0,
    0x0, 0x5f, 0xf5, 0xbf, 0xf0, 0x0, 0x0, 0x5,
    0xff, 0x5b, 0xff, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0xbf, 0xf0, 0x0, 0x0, 0x5, 0xff, 0x5b, 0xff,
    0x0, 0x0, 0x0, 0x5f, 0xf5, 0xbf, 0xf0, 0x0,
    0x0, 0x5, 0xff, 0x5b, 0xff, 0x0, 0x0, 0x0,
    0x5f, 0xf5,

    /* U+0069 "i" */
    0xef, 0x7e, 0xf7, 0xef, 0x70, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0xef, 0x7e, 0xf7, 0xef,
    0x7e, 0xf7, 0xef, 0x7e, 0xf7, 0xef, 0x7e, 0xf7,
    0xef, 0x7e, 0xf7, 0xef, 0x7e, 0xf7,

    /* U+006C "l" */
    0xcf, 0x9c, 0xf9, 0xcf, 0x9c, 0xf9, 0xcf, 0x9c,
    0xf9, 0xcf, 0x9c, 0xf9, 0xcf, 0x9c, 0xf9, 0xcf,
    0x9c, 0xf9, 0xcf, 0x9c, 0xf9, 0xcf, 0x9c, 0xf9,
    0xcf, 0x9c, 0xf9, 0xcf, 0x9c, 0xf9,

    /* U+006F "o" */
    0x0, 0x4, 0xbe, 0xfe, 0xa2, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xa, 0xff, 0xb4,
    0x25, 0xdf, 0xf6, 0x4, 0xff, 0x90, 0x0, 0x0,
    0xcf, 0xf1, 0xaf, 0xf0, 0x0, 0x0, 0x3, 0xff,
    0x6d, 0xfb, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xef,
    0xa0, 0x0, 0x0, 0x0, 0xef, 0xbd, 0xfb, 0x0,
    0x0, 0x0, 0xf, 0xfa, 0xaf, 0xf0, 0x0, 0x0,
    0x3, 0xff, 0x64, 0xff, 0x90, 0x0, 0x0, 0xcf,
    0xf1, 0xa, 0xff, 0xb3, 0x25, 0xdf, 0xf7, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x4,
    0xbe, 0xfe, 0xa3, 0x0, 0x0,

    /* U+0070 "p" */
    0x9f, 0xc0, 0x8d, 0xfe, 0xa3, 0x0, 0x9, 0xfc,
    0xcf, 0xff, 0xff, 0xf7, 0x0, 0x9f, 0xff, 0xa3,
    0x25, 0xdf, 0xf4, 0x9, 0xff, 0xa0, 0x0, 0x2,
    0xff, 0xc0, 0x9f, 0xf1, 0x0, 0x0, 0xa, 0xff,
    0x9, 0xfe, 0x0, 0x0, 0x0, 0x7f, 0xf2, 0x9f,
    0xc0, 0x0, 0x0, 0x5, 0xff, 0x39, 0xfe, 0x0,
    0x0, 0x0, 0x7f, 0xf2, 0x9f, 0xf2, 0x0, 0x0,
    0xa, 0xff, 0x9, 0xff, 0xa0, 0x0, 0x2, 0xff,
    0xb0, 0x9f, 0xff, 0xa3, 0x26, 0xef, 0xf3, 0x9,
    0xfc, 0xbf, 0xff, 0xff, 0xf6, 0x0, 0x9f, 0xc0,
    0x7d, 0xfe, 0xa3, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2e,
    0xee, 0xef, 0xfe, 0xee, 0xed, 0x0, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xd4, 0x22, 0x5a,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x2, 0xae, 0xff, 0xd7
    
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 240, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 240, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 130, .adv_w = 240, .box_w = 3, .box_h = 20, .ofs_x = 6, .ofs_y = 1},
    {.bitmap_index = 160, .adv_w = 240, .box_w = 3, .box_h = 20, .ofs_x = 6, .ofs_y = 1},
    {.bitmap_index = 190, .adv_w = 240, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 275, .adv_w = 240, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 392, .adv_w = 240, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = 1}
   
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x28, 0x49, 0x4c, 0x4f, 0x50, 0x54
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 85, .glyph_id_start = 1,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 7, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA  所有自定义数据
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
 /*Store all the custom data of the font  存储字体的所有自定义数据 */
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR >= 8
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT  公共的字体
 *----------------*/

 /*Initialize a public general font descriptor  初始化公共通用字体描述符 */
 #if LV_VERSION_CHECK(8, 0, 0)
 const lv_font_t lv_font_start = {
 #else
 lv_font_t lv_font_start = {
 #endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 25,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
    .fallback = NULL,
    .user_data = NULL
};


 #endif /*#if LV_FONT_START*/

